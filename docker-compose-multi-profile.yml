services:
  # Car Profile Services
  osrm-car-mld:
    image: ghcr.io/project-osrm/osrm-backend:latest
    container_name: osrm-car-mld
    ports:
      - "5000:5000"
    volumes:
      - ./data:/data
    command: osrm-routed --algorithm mld /data/northern-zone-latest-car-mld.osrm
    restart: unless-stopped
    mem_limit: 4g
    environment:
      - OSRM_THREADS=2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  osrm-car-ch:
    image: ghcr.io/project-osrm/osrm-backend:latest
    container_name: osrm-car-ch
    ports:
      - "5001:5000"
    volumes:
      - ./data:/data
    command: osrm-routed --algorithm ch /data/northern-zone-latest-car-ch.osrm
    restart: unless-stopped
    mem_limit: 4g
    environment:
      - OSRM_THREADS=2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Bicycle Profile Services
  osrm-bicycle-mld:
    image: ghcr.io/project-osrm/osrm-backend:latest
    container_name: osrm-bicycle-mld
    ports:
      - "5010:5000"
    volumes:
      - ./data:/data
    command: osrm-routed --algorithm mld /data/northern-zone-latest-bicycle-mld.osrm
    restart: unless-stopped
    mem_limit: 4g
    environment:
      - OSRM_THREADS=2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  osrm-bicycle-ch:
    image: ghcr.io/project-osrm/osrm-backend:latest
    container_name: osrm-bicycle-ch
    ports:
      - "5011:5000"
    volumes:
      - ./data:/data
    command: osrm-routed --algorithm ch /data/northern-zone-latest-bicycle-ch.osrm
    restart: unless-stopped
    mem_limit: 4g
    environment:
      - OSRM_THREADS=2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s



  # Frontend Service
  osrm-frontend:
    image: osrm/osrm-frontend:latest
    container_name: osrm-frontend
    ports:
      - "9966:9966"
    environment:
      - OSRM_BACKEND=http://osrm-car-mld:5000
    depends_on:
      - osrm-car-mld
      - osrm-car-ch
      - osrm-bicycle-mld
      - osrm-bicycle-ch
    restart: unless-stopped

  # Load Balancer / API Gateway (Optional)
  osrm-gateway:
    image: nginx:alpine
    container_name: osrm-gateway
    ports:
      - "8080:80"
    volumes:
      - ./nginx-multi-profile.conf:/etc/nginx/nginx.conf
    depends_on:
      - osrm-car-mld
      - osrm-car-ch
      - osrm-bicycle-mld
      - osrm-bicycle-ch
    restart: unless-stopped

networks:
  default:
    name: osrm-multi-profile-network
