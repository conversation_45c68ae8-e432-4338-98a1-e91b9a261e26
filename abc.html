<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSRM Multi-Profile Routing Demo - Northern India</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
        }
        
        .control-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        
        .control-group select, .control-group input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .control-group select:focus, .control-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .results-container {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .route-result {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .route-header {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 8px;
            color: #333;
        }
        
        .route-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 0.9em;
            color: #666;
        }
        
        .service-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .status-offline {
            border-left-color: #dc3545;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #28a745;
            margin-right: 8px;
        }
        
        .status-offline .status-indicator {
            background: #dc3545;
        }
        
        .coordinates-display {
            font-family: monospace;
            background: #f1f3f4;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .algorithm-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 8px;
        }
        
        .badge-mld {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .badge-ch {
            background: #fce4ec;
            color: #c2185b;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇮🇳 OSRM Multi-Profile Routing Demo</h1>
            <p>Advanced Routing for Northern India - Multiple Transportation Modes & Algorithms</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🚗</div>
                <div class="feature-title">Car/Driving Mode</div>
                <div class="feature-description">
                    Optimized routing for motor vehicles with traffic rules, speed limits, and road restrictions.
                    Supports both MLD (fast) and CH (optimized) algorithms.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🚴</div>
                <div class="feature-title">Bicycle/Cycling Mode</div>
                <div class="feature-description">
                    Bicycle-friendly routing with appropriate speed limits, bike paths, and cycling infrastructure.
                    Perfect for urban cycling and recreational routes.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">MLD Algorithm</div>
                <div class="feature-description">
                    Multi-Level Dijkstra algorithm provides fast single-point to single-point routing.
                    Ideal for real-time route calculations and quick responses.
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">CH Algorithm</div>
                <div class="feature-description">
                    Contraction Hierarchies algorithm optimized for multi-waypoint trip planning.
                    Perfect for complex routes with multiple stops and trip optimization.
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🧪 Interactive Route Demo</h2>
            <p>Test different transportation modes and algorithms with real Northern India locations</p>
            
            <div class="demo-controls">
                <div class="control-group">
                    <label for="mode-select">Transportation Mode:</label>
                    <select id="mode-select">
                        <option value="car">🚗 Car/Driving</option>
                        <option value="bicycle">🚴 Bicycle/Cycling</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="algorithm-select">Algorithm:</label>
                    <select id="algorithm-select">
                        <option value="mld">⚡ MLD (Fast)</option>
                        <option value="ch">🎯 CH (Optimized)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="route-select">Sample Routes:</label>
                    <select id="route-select">
                        <option value="delhi-gurgaon">Delhi → Gurgaon</option>
                        <option value="delhi-chandigarh">Delhi → Chandigarh</option>
                        <option value="delhi-jaipur">Delhi → Jaipur</option>
                        <option value="chandigarh-shimla">Chandigarh → Shimla</option>
                        <option value="multi-golden">Multi: Golden Triangle</option>
                        <option value="multi-punjab">Multi: Punjab Circuit</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="calculateRoute()">Calculate Route</button>
                </div>
            </div>
            
            <div id="results" class="results-container" style="display: none;">
                <h3>📊 Route Results</h3>
                <div id="route-results"></div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🌐 Service Status</h2>
            <p>Real-time status of OSRM multi-profile services</p>
            
            <div class="service-status">
                <div class="status-card" id="status-car-mld">
                    <span class="status-indicator"></span>
                    <strong>Car MLD</strong> <span class="algorithm-badge badge-mld">MLD</span>
                    <div class="coordinates-display">http://localhost:5000</div>
                </div>
                
                <div class="status-card" id="status-car-ch">
                    <span class="status-indicator"></span>
                    <strong>Car CH</strong> <span class="algorithm-badge badge-ch">CH</span>
                    <div class="coordinates-display">http://localhost:5001</div>
                </div>
                
                <div class="status-card" id="status-bicycle-mld">
                    <span class="status-indicator"></span>
                    <strong>Bicycle MLD</strong> <span class="algorithm-badge badge-mld">MLD</span>
                    <div class="coordinates-display">http://localhost:5010</div>
                </div>
                
                <div class="status-card" id="status-bicycle-ch">
                    <span class="status-indicator"></span>
                    <strong>Bicycle CH</strong> <span class="algorithm-badge badge-ch">CH</span>
                    <div class="coordinates-display">http://localhost:5011</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample coordinates for Northern India locations
        const locations = {
            'delhi': '77.209,28.6139',
            'gurgaon': '77.0266,28.4595',
            'chandigarh': '76.7794,30.7333',
            'jaipur': '75.7873,26.9124',
            'shimla': '77.1734,31.1048',
            'agra': '78.0081,27.1767'
        };
        
        // Service URLs
        const serviceUrls = {
            'car-mld': 'http://localhost:5000',
            'car-ch': 'http://localhost:5001',
            'bicycle-mld': 'http://localhost:5010',
            'bicycle-ch': 'http://localhost:5011'
        };
        
        // Check service status on page load
        window.addEventListener('load', function() {
            checkAllServices();
            setInterval(checkAllServices, 30000); // Check every 30 seconds
        });
        
        async function checkAllServices() {
            for (const [key, url] of Object.entries(serviceUrls)) {
                await checkServiceStatus(key, url);
            }
        }
        
        async function checkServiceStatus(serviceKey, url) {
            const statusElement = document.getElementById(`status-${serviceKey}`);
            
            try {
                const response = await fetch(url + '/', { 
                    method: 'GET',
                    mode: 'no-cors',
                    timeout: 5000 
                });
                
                // Since we're using no-cors, we can't check the actual response
                // but if no error is thrown, the service is likely running
                statusElement.classList.remove('status-offline');
            } catch (error) {
                statusElement.classList.add('status-offline');
            }
        }
        
        async function calculateRoute() {
            const mode = document.getElementById('mode-select').value;
            const algorithm = document.getElementById('algorithm-select').value;
            const routeType = document.getElementById('route-select').value;
            
            const resultsContainer = document.getElementById('results');
            const routeResults = document.getElementById('route-results');
            
            resultsContainer.style.display = 'block';
            routeResults.innerHTML = '<div style="text-align: center; padding: 20px;">🔄 Calculating route...</div>';
            
            try {
                if (routeType.startsWith('multi-')) {
                    await calculateMultiWaypointRoute(mode, algorithm, routeType);
                } else {
                    await calculateSingleRoute(mode, algorithm, routeType);
                }
            } catch (error) {
                routeResults.innerHTML = `
                    <div class="route-result" style="border-left-color: #dc3545;">
                        <div class="route-header">❌ Error</div>
                        <div class="route-details">
                            <div>Error: ${error.message}</div>
                            <div>Please ensure OSRM services are running</div>
                        </div>
                    </div>
                `;
            }
        }
        
        async function calculateSingleRoute(mode, algorithm, routeType) {
            const [from, to] = routeType.split('-');
            const fromCoords = locations[from];
            const toCoords = locations[to];
            
            const serviceKey = `${mode}-${algorithm}`;
            const baseUrl = serviceUrls[serviceKey];
            const profileName = mode === 'car' ? 'driving' : 'cycling';
            
            const url = `${baseUrl}/route/v1/${profileName}/${fromCoords};${toCoords}?overview=false&alternatives=false&steps=false`;
            
            // Simulate API call (since we can't make actual CORS requests in demo)
            const mockResponse = generateMockRouteResponse(mode, algorithm, from, to);
            displayRouteResult(mockResponse, mode, algorithm, `${from} → ${to}`);
        }
        
        async function calculateMultiWaypointRoute(mode, algorithm, routeType) {
            let waypoints, routeName;
            
            if (routeType === 'multi-golden') {
                waypoints = `${locations.delhi};${locations.agra};${locations.jaipur};${locations.delhi}`;
                routeName = 'Golden Triangle (Delhi-Agra-Jaipur)';
            } else if (routeType === 'multi-punjab') {
                waypoints = `${locations.delhi};${locations.chandigarh};${locations.shimla}`;
                routeName = 'Punjab Circuit (Delhi-Chandigarh-Shimla)';
            }
            
            const serviceKey = `${mode}-ch`; // Always use CH for multi-waypoint
            const baseUrl = serviceUrls[serviceKey];
            const profileName = mode === 'car' ? 'driving' : 'cycling';
            
            // Simulate trip optimization API call
            const mockResponse = generateMockTripResponse(mode, 'ch', routeName);
            displayRouteResult(mockResponse, mode, 'ch', routeName);
        }
        
        function generateMockRouteResponse(mode, algorithm, from, to) {
            // Generate realistic mock data based on mode and route
            const baseDistances = {
                'delhi-gurgaon': 28.5,
                'delhi-chandigarh': 245.0,
                'delhi-jaipur': 280.0,
                'chandigarh-shimla': 115.0
            };
            
            const baseSpeeds = {
                'car': 45,
                'bicycle': 18
            };
            
            const routeKey = `${from}-${to}`;
            const distance = baseDistances[routeKey] || 150;
            const speed = baseSpeeds[mode];
            const duration = (distance / speed) * 3600; // Convert to seconds
            
            return {
                distance: distance * 1000, // Convert to meters
                duration: duration,
                mode: mode,
                algorithm: algorithm
            };
        }
        
        function generateMockTripResponse(mode, algorithm, routeName) {
            const baseSpeeds = {
                'car': 45,
                'bicycle': 18
            };
            
            const distance = routeName.includes('Golden') ? 850 : 360;
            const speed = baseSpeeds[mode];
            const duration = (distance / speed) * 3600;
            
            return {
                distance: distance * 1000,
                duration: duration,
                mode: mode,
                algorithm: algorithm,
                optimized: true
            };
        }
        
        function displayRouteResult(result, mode, algorithm, routeName) {
            const routeResults = document.getElementById('route-results');
            
            const distanceKm = (result.distance / 1000).toFixed(2);
            const hours = Math.floor(result.duration / 3600);
            const minutes = Math.floor((result.duration % 3600) / 60);
            const avgSpeed = ((result.distance / 1000) / (result.duration / 3600)).toFixed(1);
            
            const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
            const modeIcon = mode === 'car' ? '🚗' : '🚴';
            const algorithmIcon = algorithm === 'mld' ? '⚡' : '🎯';
            const optimizedText = result.optimized ? ' (Trip Optimized)' : '';
            
            routeResults.innerHTML = `
                <div class="route-result">
                    <div class="route-header">
                        ${modeIcon} ${routeName} ${algorithmIcon} ${algorithm.toUpperCase()}${optimizedText}
                    </div>
                    <div class="route-details">
                        <div><strong>Distance:</strong> ${distanceKm} km</div>
                        <div><strong>Duration:</strong> ${durationText}</div>
                        <div><strong>Avg Speed:</strong> ${avgSpeed} km/h</div>
                        <div><strong>Algorithm:</strong> ${algorithm.toUpperCase()}</div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
