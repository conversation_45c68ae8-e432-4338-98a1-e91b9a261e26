#!/bin/bash

# Full India Map Processing Script
# Processes the complete India OSM data for OSRM with multiple profiles and algorithms

set -e

echo "🇮🇳 Full India OSRM Processing Script"
echo "====================================="
echo ""

# Configuration
PROFILE=${1:-car}  # Default to car profile, can be: car, bicycle, foot
ALGORITHM=${2:-mld}  # Default to MLD algorithm, can be: mld, ch

echo "🔧 Configuration:"
echo "   Profile: $PROFILE"
echo "   Algorithm: $ALGORITHM"
echo ""

# Validate profile
case $PROFILE in
    car|bicycle|foot)
        echo "✅ Valid profile: $PROFILE"
        ;;
    *)
        echo "❌ Invalid profile: $PROFILE"
        echo "Valid profiles: car, bicycle, foot"
        exit 1
        ;;
esac

# Validate algorithm
case $ALGORITHM in
    mld|ch)
        echo "✅ Valid algorithm: $ALGORITHM"
        ;;
    *)
        echo "❌ Invalid algorithm: $ALGORITHM"
        echo "Valid algorithms: mld, ch"
        exit 1
        ;;
esac

# Check if India OSM file exists
if [ ! -f "data/india-latest.osm.pbf" ]; then
    echo "❌ India OSM file not found: data/india-latest.osm.pbf"
    echo "Please download it first."
    exit 1
fi

# Check file size
FILE_SIZE=$(ls -lh data/india-latest.osm.pbf | awk '{print $5}')
echo "📁 India OSM file size: $FILE_SIZE"

# Check available memory
echo "💾 Checking system resources..."
docker system df

echo ""
echo "🔄 Starting Full India Processing with $PROFILE profile and $ALGORITHM algorithm..."
echo "This will take 1-3 hours depending on your system."
echo ""

# Set output file names based on profile and algorithm
OUTPUT_BASE="india-latest-${PROFILE}-${ALGORITHM}"

# Clean up any existing partial files
echo "🧹 Cleaning up partial files..."
rm -f "data/${OUTPUT_BASE}.osrm"*

# Step 1: Extract
echo "📊 Step 1/3: Extracting road network with $PROFILE profile..."
docker run --rm -t \
    -v "${PWD}/data:/data" \
    -v "${PWD}/profiles:/profiles" \
    --memory=24g \
    --memory-swap=28g \
    --name "osrm-extract-india-${PROFILE}" \
    ghcr.io/project-osrm/osrm-backend \
    osrm-extract -p "/profiles/${PROFILE}.lua" /data/india-latest.osm.pbf

if [ $? -ne 0 ]; then
    echo "❌ Extract failed. Try with more memory or use regional zones."
    exit 1
fi

# Rename the extracted files to include profile and algorithm
echo "🔄 Renaming extracted files to include profile and algorithm..."
for file in data/india-latest.osrm*; do
    if [ -f "$file" ]; then
        new_file="${file/india-latest.osrm/$OUTPUT_BASE.osrm}"
        mv "$file" "$new_file"
        echo "   Renamed: $file -> $new_file"
    fi
done

# Step 2: Process based on algorithm
if [ "$ALGORITHM" = "mld" ]; then
    # MLD Algorithm: Partition and Customize
    echo "🔀 Step 2/3: Partitioning graph for MLD..."
    docker run --rm -t \
        -v "${PWD}/data:/data" \
        --memory=24g \
        --memory-swap=28g \
        --name "osrm-partition-india-${PROFILE}" \
        ghcr.io/project-osrm/osrm-backend \
        osrm-partition "/data/${OUTPUT_BASE}.osrm"

    if [ $? -ne 0 ]; then
        echo "❌ Partition failed."
        exit 1
    fi

    echo "⚙️  Step 3/3: Customizing for ${PROFILE} profile with MLD..."
    docker run --rm -t \
        -v "${PWD}/data:/data" \
        --memory=24g \
        --memory-swap=28g \
        --name "osrm-customize-india-${PROFILE}" \
        ghcr.io/project-osrm/osrm-backend \
        osrm-customize "/data/${OUTPUT_BASE}.osrm"

    if [ $? -ne 0 ]; then
        echo "❌ Customize failed."
        exit 1
    fi
else
    # CH Algorithm: Contract
    echo "🔀 Step 2/2: Contracting graph for CH..."
    docker run --rm -t \
        -v "${PWD}/data:/data" \
        --memory=24g \
        --memory-swap=28g \
        --name "osrm-contract-india-${PROFILE}" \
        ghcr.io/project-osrm/osrm-backend \
        osrm-contract "/data/${OUTPUT_BASE}.osrm"

    if [ $? -ne 0 ]; then
        echo "❌ Contract failed."
        exit 1
    fi
fi

echo ""
echo "✅ Full India processing completed successfully!"
echo "   Profile: $PROFILE"
echo "   Algorithm: $ALGORITHM"
echo ""
echo "📊 Processed files:"
ls -lh "data/${OUTPUT_BASE}.osrm"* | head -10

echo ""
echo "🚀 To use the full India map:"
echo "1. Update docker-compose.yml to use india-latest.osrm"
echo "2. Restart services: docker-compose down && docker-compose up -d"
echo ""
echo "🔧 Or use the switch script:"
echo "./switch-zone.sh"
