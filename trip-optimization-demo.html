<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSRM Trip Optimization Demo</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #2c3e50; 
            text-align: center; 
            margin-bottom: 30px;
        }
        .demo-section { 
            margin: 30px 0; 
            padding: 20px; 
            border: 2px solid #e9ecef; 
            border-radius: 8px;
        }
        .demo-section h2 { 
            color: #34495e; 
            margin-bottom: 15px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .route-card { 
            padding: 15px; 
            border-radius: 8px; 
            border: 1px solid #dee2e6;
        }
        .route-card.original { 
            background: #fff3cd; 
            border-color: #ffeaa7;
        }
        .route-card.optimized { 
            background: #d1ecf1; 
            border-color: #bee5eb;
        }
        .route-card h3 { 
            margin: 0 0 10px 0; 
            color: #2c3e50;
        }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            margin: 5px 0;
        }
        .savings { 
            background: #d4edda; 
            border: 1px solid #c3e6cb; 
            border-radius: 5px; 
            padding: 10px; 
            margin: 15px 0; 
            text-align: center; 
            font-weight: bold; 
            color: #155724;
        }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px 5px;
        }
        button:hover { 
            background: #0056b3; 
        }
        .loading { 
            text-align: center; 
            color: #6c757d; 
            font-style: italic;
        }
        .explanation { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
        }
        .city-list { 
            font-family: monospace; 
            background: #f1f3f4; 
            padding: 10px; 
            border-radius: 4px; 
            margin: 10px 0;
        }
        .arrow { 
            color: #007bff; 
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 OSRM Trip Optimization Demo</h1>
        
        <div class="explanation">
            <h2>🤔 How Trip Optimization Works (Simple Explanation)</h2>
            <p><strong>Imagine you're a delivery driver</strong> who needs to visit multiple cities and return home:</p>
            <ul>
                <li><strong>Without Optimization:</strong> You visit cities in the order given, even if it means lots of backtracking</li>
                <li><strong>With CH Optimization:</strong> The algorithm finds the shortest route that visits all cities</li>
                <li><strong>Result:</strong> You save time, fuel, and distance!</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>📍 Select Cities to Visit</h2>
            <button onclick="testGoldenTriangle()">Golden Triangle (Delhi-Agra-Jaipur)</button>
            <button onclick="testPunjabCircuit()">Punjab Circuit (Delhi-Chandigarh-Amritsar-Ludhiana)</button>
            <button onclick="testRandomOrder()">Random 4 Cities</button>
            <button onclick="testCustom()">Custom Route</button>
        </div>

        <div id="results" style="display: none;">
            <div class="comparison">
                <div class="route-card original">
                    <h3>🚗 Original Order (MLD Route)</h3>
                    <div id="original-route"></div>
                </div>

                <div class="route-card optimized">
                    <h3>🎯 One-Way Optimized (Table + Route)</h3>
                    <div id="oneway-optimized"></div>
                </div>

                <div class="route-card optimized">
                    <h3>🔄 Round Trip Optimized (CH Trip)</h3>
                    <div id="roundtrip-optimized"></div>
                </div>
            </div>
            
            <div id="savings" class="savings" style="display: none;"></div>
            
            <div class="explanation">
                <h3>📊 What Happened?</h3>
                <div id="explanation-text"></div>
            </div>
        </div>

        <div id="loading" class="loading" style="display: none;">
            🔄 Calculating optimal routes...
        </div>
    </div>

    <script>
        const locations = {
            "Delhi": "77.209,28.6139",
            "Gurgaon": "77.0266,28.4595", 
            "Chandigarh": "76.7794,30.7333",
            "Amritsar": "74.8723,31.6340",
            "Ludhiana": "75.8573,30.9010",
            "Jaipur": "75.7873,26.9124",
            "Agra": "78.0081,27.1767",
            "Shimla": "77.1734,31.1048"
        };

        function formatDistance(meters) {
            return (meters / 1000).toFixed(1) + ' km';
        }

        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
        }

        async function compareRoutes(cityNames) {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';

            const coords = cityNames.map(name => locations[name]).join(';');

            try {
                // Get original route (MLD)
                const originalResponse = await fetch(`http://localhost:5000/route/v1/driving/${coords}?overview=full&geometries=geojson`);
                const originalData = await originalResponse.json();

                // Get optimized trip (CH - round trip)
                const roundTripResponse = await fetch(`http://localhost:5001/trip/v1/driving/${coords}?overview=full&geometries=geojson`);
                const roundTripData = await roundTripResponse.json();

                // Get one-way optimized route using Table API + Route API
                const oneWayOptimized = await getOptimizedOneWayRoute(cityNames);

                if (originalData.code === 'Ok' && roundTripData.code === 'Ok') {
                    displayComparison(cityNames, originalData.routes[0], roundTripData.trips[0], roundTripData.waypoints, oneWayOptimized);
                } else {
                    alert('Error calculating routes. Please ensure OSRM services are running.');
                }
            } catch (error) {
                alert('Error connecting to OSRM services: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        async function getOptimizedOneWayRoute(cityNames) {
            if (cityNames.length <= 2) {
                return null; // No optimization needed for 2 cities
            }

            try {
                // Get distance matrix using Table API
                const coords = cityNames.map(name => locations[name]).join(';');
                const tableResponse = await fetch(`http://localhost:5000/table/v1/driving/${coords}`);
                const tableData = await tableResponse.json();

                if (tableData.code !== 'Ok') {
                    return null;
                }

                // Find optimal one-way route using nearest neighbor heuristic
                const distances = tableData.distances;
                const optimalOrder = findOptimalOneWayOrder(distances);
                const optimizedCityNames = optimalOrder.map(index => cityNames[index]);

                // Get the actual route for the optimized order
                const optimizedCoords = optimizedCityNames.map(name => locations[name]).join(';');
                const routeResponse = await fetch(`http://localhost:5000/route/v1/driving/${optimizedCoords}?overview=full&geometries=geojson`);
                const routeData = await routeResponse.json();

                if (routeData.code === 'Ok') {
                    return {
                        route: routeData.routes[0],
                        cityNames: optimizedCityNames,
                        originalOrder: optimalOrder
                    };
                }
            } catch (error) {
                console.error('Error calculating one-way optimization:', error);
            }

            return null;
        }

        function findOptimalOneWayOrder(distances) {
            const n = distances.length;
            if (n <= 2) return [0, 1];

            // Simple nearest neighbor heuristic starting from city 0
            const visited = new Array(n).fill(false);
            const order = [0];
            visited[0] = true;
            let current = 0;

            for (let i = 1; i < n; i++) {
                let nearest = -1;
                let minDistance = Infinity;

                for (let j = 0; j < n; j++) {
                    if (!visited[j] && distances[current][j] < minDistance) {
                        minDistance = distances[current][j];
                        nearest = j;
                    }
                }

                if (nearest !== -1) {
                    order.push(nearest);
                    visited[nearest] = true;
                    current = nearest;
                }
            }

            return order;
        }

        function displayComparison(cityNames, originalRoute, roundTrip, roundTripWaypoints, oneWayOptimized) {
            // Original route
            document.getElementById('original-route').innerHTML = `
                <div class="city-list">Route: ${cityNames.join(' → ')}</div>
                <div class="metric">
                    <span>Distance:</span>
                    <span>${formatDistance(originalRoute.distance)}</span>
                </div>
                <div class="metric">
                    <span>Duration:</span>
                    <span>${formatDuration(originalRoute.duration)}</span>
                </div>
                <div class="metric">
                    <span>Algorithm:</span>
                    <span>MLD (follows exact order)</span>
                </div>
            `;

            // One-way optimized route
            if (oneWayOptimized) {
                document.getElementById('oneway-optimized').innerHTML = `
                    <div class="city-list">Route: ${oneWayOptimized.cityNames.join(' → ')}</div>
                    <div class="metric">
                        <span>Distance:</span>
                        <span>${formatDistance(oneWayOptimized.route.distance)}</span>
                    </div>
                    <div class="metric">
                        <span>Duration:</span>
                        <span>${formatDuration(oneWayOptimized.route.duration)}</span>
                    </div>
                    <div class="metric">
                        <span>Algorithm:</span>
                        <span>Table + Route (one-way optimized)</span>
                    </div>
                `;
            } else {
                document.getElementById('oneway-optimized').innerHTML = `
                    <div class="city-list">Not applicable for 2 cities</div>
                    <div class="metric">
                        <span>Note:</span>
                        <span>One-way optimization only works with 3+ cities</span>
                    </div>
                `;
            }

            // Get round trip optimized order
            const roundTripOrder = roundTripWaypoints.map((wp, index) => ({
                originalIndex: wp.waypoint_index,
                city: cityNames[wp.waypoint_index],
                position: index
            })).sort((a, b) => a.position - b.position);

            const roundTripCityNames = roundTripOrder.map(item => item.city);

            document.getElementById('roundtrip-optimized').innerHTML = `
                <div class="city-list">Route: ${roundTripCityNames.join(' → ')} → ${roundTripCityNames[0]}</div>
                <div class="metric">
                    <span>Distance:</span>
                    <span>${formatDistance(roundTrip.distance)}</span>
                </div>
                <div class="metric">
                    <span>Duration:</span>
                    <span>${formatDuration(roundTrip.duration)}</span>
                </div>
                <div class="metric">
                    <span>Algorithm:</span>
                    <span>CH (round trip optimized)</span>
                </div>
            `;

            // Calculate savings for one-way optimization
            let savingsText = '';
            if (oneWayOptimized) {
                const oneWayDistanceSaving = originalRoute.distance - oneWayOptimized.route.distance;
                const oneWayTimeSaving = originalRoute.duration - oneWayOptimized.route.duration;
                const oneWayDistanceSavingPercent = ((oneWayDistanceSaving / originalRoute.distance) * 100).toFixed(1);
                const oneWayTimeSavingPercent = ((oneWayTimeSaving / originalRoute.duration) * 100).toFixed(1);

                if (oneWayDistanceSaving > 0) {
                    savingsText += `
                        💰 One-Way Optimization Savings: ${formatDistance(Math.abs(oneWayDistanceSaving))} (${Math.abs(oneWayDistanceSavingPercent)}%) shorter,
                        ${formatDuration(Math.abs(oneWayTimeSaving))} (${Math.abs(oneWayTimeSavingPercent)}%) faster!<br>
                    `;
                } else {
                    savingsText += `
                        ℹ️ One-Way: Original order was already efficient!
                        Difference: ${formatDistance(Math.abs(oneWayDistanceSaving))} (${Math.abs(oneWayDistanceSavingPercent)}%)<br>
                    `;
                }
            }

            savingsText += `
                🔄 Round Trip: ${formatDistance(roundTrip.distance)} total distance (includes return to start)
            `;

            document.getElementById('savings').innerHTML = savingsText;
            document.getElementById('savings').style.display = 'block';

            // Explanation
            let explanationText = `
                <p><strong>Original Order:</strong> ${cityNames.join(' → ')}</p>
                <p><strong>Optimized Order:</strong> ${optimizedCityNames.join(' → ')}</p>
                <p><strong>What the CH algorithm did:</strong></p>
                <ul>
            `;

            if (JSON.stringify(cityNames) !== JSON.stringify(optimizedCityNames)) {
                explanationText += `<li>🔄 <strong>Reordered the cities</strong> to minimize total travel distance</li>`;
                explanationText += `<li>🧮 <strong>Calculated all possible routes</strong> and chose the shortest one</li>`;
                explanationText += `<li>📍 <strong>Kept the starting point</strong> but optimized the sequence of visits</li>`;
            } else {
                explanationText += `<li>✅ <strong>Confirmed the original order was already optimal</strong></li>`;
                explanationText += `<li>🔍 <strong>Checked all possible routes</strong> and found no better alternative</li>`;
            }

            explanationText += `</ul>`;
            document.getElementById('explanation-text').innerHTML = explanationText;

            document.getElementById('results').style.display = 'block';
        }

        function testGoldenTriangle() {
            compareRoutes(['Delhi', 'Agra', 'Jaipur']);
        }

        function testPunjabCircuit() {
            compareRoutes(['Delhi', 'Chandigarh', 'Amritsar', 'Ludhiana']);
        }

        function testRandomOrder() {
            compareRoutes(['Delhi', 'Jaipur', 'Agra', 'Gurgaon']);
        }

        function testCustom() {
            const cities = prompt('Enter city names separated by commas (available: Delhi, Gurgaon, Chandigarh, Amritsar, Ludhiana, Jaipur, Agra, Shimla):');
            if (cities) {
                const cityList = cities.split(',').map(c => c.trim());
                const validCities = cityList.filter(city => locations[city]);
                if (validCities.length >= 2) {
                    compareRoutes(validCities);
                } else {
                    alert('Please enter at least 2 valid city names.');
                }
            }
        }
    </script>
</body>
</html>
