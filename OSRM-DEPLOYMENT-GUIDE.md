# OSRM Production Deployment Guide

## 🎯 **1. Single Container vs Multi-Container Architecture**

### **Multi-Container (Current Setup) - RECOMMENDED**

**Pros:**
- ✅ **Better Performance**: Each service optimized for specific use case
- ✅ **Fault Isolation**: One service failure doesn't affect others
- ✅ **Independent Scaling**: Scale car services separately from bicycle
- ✅ **Memory Optimization**: Each container loads only required data
- ✅ **Algorithm Specialization**: MLD vs CH optimized separately

**Cons:**
- ❌ More containers to manage
- ❌ Higher memory usage overall

### **Single Container Alternative**

**Possible but NOT recommended for production:**
```bash
# Single container with multiple profiles (theoretical)
docker run -v ./data:/data osrm-backend \
  osrm-routed --algorithm mld \
  /data/car.osrm \
  /data/bicycle.osrm \
  /data/foot.osrm
```

**Why NOT recommended:**
- ❌ Single point of failure
- ❌ Cannot scale individual profiles
- ❌ Memory conflicts between algorithms
- ❌ Complex request routing logic needed

## 📊 **2. Resource Requirements**

### **For Northern India (Current Setup)**

| Component | RAM per Container | Storage per Profile | Total |
|-----------|------------------|-------------------|-------|
| Car MLD | 2-4 GB | 8-12 GB | |
| Car CH | 3-6 GB | 10-15 GB | |
| Bicycle MLD | 2-4 GB | 8-12 GB | |
| Bicycle CH | 3-6 GB | 10-15 GB | |
| **TOTAL** | **10-20 GB RAM** | **34-54 GB Storage** | |

### **For Full India Map**

| Scope | RAM Required | Storage Required | Processing Time |
|-------|-------------|-----------------|----------------|
| **Northern India** | 10-20 GB | 50 GB | 2-4 hours |
| **Full India** | 40-80 GB | 200-300 GB | 8-16 hours |
| **Production Setup** | 64-128 GB | 500 GB SSD | 12-24 hours |

### **Recommended Production Server**

```yaml
Minimum Specs:
  CPU: 16 cores
  RAM: 64 GB
  Storage: 500 GB SSD
  Network: 1 Gbps

Optimal Specs:
  CPU: 32 cores  
  RAM: 128 GB
  Storage: 1 TB NVMe SSD
  Network: 10 Gbps
```

## 🔄 **3. Map Update Strategy (Zero Downtime)**

### **Blue-Green Deployment Approach**

```bash
#!/bin/bash
# Zero-downtime map update script

# Step 1: Download new OSM data
wget -O data/india-latest-new.osm.pbf \
  https://download.geofabrik.de/asia/india-latest.osm.pbf

# Step 2: Process new data (parallel to running services)
./process-northern-zone-multi.sh "car,bicycle" "mld,ch" \
  --input data/india-latest-new.osm.pbf \
  --output data/new/

# Step 3: Start new services on different ports
docker-compose -f docker-compose-new.yml up -d

# Step 4: Health check new services
./health-check-services.sh --ports 6000,6001,6010,6011

# Step 5: Update load balancer to point to new services
./update-load-balancer.sh --switch-to-new

# Step 6: Stop old services
docker-compose -f docker-compose-old.yml down

# Step 7: Clean up old data
rm -rf data/old/
mv data/current/ data/old/
mv data/new/ data/current/
```

### **Automated Update Process**

```yaml
# docker-compose-update.yml
version: '3.8'
services:
  map-updater:
    image: osrm-map-updater:latest
    volumes:
      - ./data:/data
      - ./scripts:/scripts
    environment:
      - UPDATE_SCHEDULE=0 2 * * 0  # Weekly on Sunday 2 AM
      - GEOFABRIK_URL=https://download.geofabrik.de/asia/india-latest.osm.pbf
    command: /scripts/automated-update.sh
```

### **Update Frequency Recommendations**

| Update Type | Frequency | Downtime | Complexity |
|------------|-----------|----------|------------|
| **Minor Updates** | Weekly | 0 minutes | Low |
| **Major Updates** | Monthly | 0 minutes | Medium |
| **Full Rebuild** | Quarterly | 5-10 minutes | High |

## 🚀 **4. Request Routing in Multi-Container Setup**

### **Current API Structure**

```bash
# Direct container access
Car MLD:     http://localhost:5000/route/v1/driving/...
Car CH:      http://localhost:5001/trip/v1/driving/...
Bicycle MLD: http://localhost:5010/route/v1/cycling/...
Bicycle CH:  http://localhost:5011/trip/v1/cycling/...

# Via API Gateway (recommended)
Gateway:     http://localhost:8080/route/v1/driving/...
             http://localhost:8080/route/v1/cycling/...
             http://localhost:8080/trip/v1/driving/...
```

### **Load Balancer Configuration**

```nginx
# nginx.conf for production
upstream osrm-car-mld {
    server osrm-car-mld-1:5000;
    server osrm-car-mld-2:5000;
    server osrm-car-mld-3:5000;
}

upstream osrm-car-ch {
    server osrm-car-ch-1:5000;
    server osrm-car-ch-2:5000;
}

server {
    listen 80;
    
    # Route API (fast, use MLD)
    location /route/ {
        proxy_pass http://osrm-car-mld;
    }
    
    # Trip API (optimization, use CH)
    location /trip/ {
        proxy_pass http://osrm-car-ch;
    }
    
    # Table API (distance matrix, use MLD)
    location /table/ {
        proxy_pass http://osrm-car-mld;
    }
}
```

## 🔧 **5. Production Deployment Checklist**

### **Infrastructure Setup**

```bash
# 1. Server provisioning
terraform apply -var="instance_type=c5.4xlarge"

# 2. Docker swarm initialization
docker swarm init

# 3. Deploy services
docker stack deploy -c docker-compose-prod.yml osrm

# 4. Setup monitoring
docker stack deploy -c monitoring-stack.yml monitoring

# 5. Configure backups
./setup-backup-cron.sh
```

### **Monitoring & Alerting**

```yaml
# monitoring-stack.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      
  alertmanager:
    image: prom/alertmanager
    ports:
      - "9093:9093"
```

## 📈 **6. Scaling Strategy**

### **Horizontal Scaling**

```yaml
# docker-compose-scaled.yml
version: '3.8'
services:
  osrm-car-mld:
    image: ghcr.io/project-osrm/osrm-backend
    deploy:
      replicas: 3  # Scale based on load
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

### **Auto-scaling Rules**

```bash
# CPU-based scaling
if cpu_usage > 80% for 5 minutes:
    scale_up(replicas + 1)
    
if cpu_usage < 30% for 10 minutes:
    scale_down(replicas - 1, min=1)

# Memory-based scaling  
if memory_usage > 90%:
    alert("Memory critical")
    
# Request-based scaling
if requests_per_second > 100:
    scale_up(car_mld_replicas + 1)
```

## 🔐 **7. Security & Best Practices**

### **Security Configuration**

```yaml
# Security headers
server {
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
}
```

### **Backup Strategy**

```bash
#!/bin/bash
# backup-osrm-data.sh

# Daily backup of processed data
tar -czf "backup/osrm-data-$(date +%Y%m%d).tar.gz" data/

# Weekly backup to cloud storage
aws s3 sync backup/ s3://osrm-backups/

# Cleanup old backups (keep 30 days)
find backup/ -name "*.tar.gz" -mtime +30 -delete
```

This guide provides a complete production deployment strategy for your OSRM setup! 🚀
