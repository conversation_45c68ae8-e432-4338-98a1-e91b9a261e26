#!/bin/bash

# Northern Zone Multi-Profile and Multi-Algorithm Processing Script
# Processes the Northern India OSM data for OSRM with multiple profiles and algorithms

set -e

echo "🇮🇳 Northern Zone Multi-Profile OSRM Processing Script"
echo "====================================================="
echo ""

# Configuration
PROFILES=${1:-"car,bicycle,foot"}  # Comma-separated list of profiles
ALGORITHMS=${2:-"mld,ch"}          # Comma-separated list of algorithms

echo "🔧 Configuration:"
echo "   Profiles: $PROFILES"
echo "   Algorithms: $ALGORITHMS"
echo ""

# Convert comma-separated strings to arrays
IFS=',' read -ra PROFILE_ARRAY <<< "$PROFILES"
IFS=',' read -ra ALGORITHM_ARRAY <<< "$ALGORITHMS"

# Validate profiles
for profile in "${PROFILE_ARRAY[@]}"; do
    case $profile in
        car|bicycle|foot)
            echo "✅ Valid profile: $profile"
            ;;
        *)
            echo "❌ Invalid profile: $profile"
            echo "Valid profiles: car, bicycle, foot"
            exit 1
            ;;
    esac
done

# Validate algorithms
for algorithm in "${ALGORITHM_ARRAY[@]}"; do
    case $algorithm in
        mld|ch)
            echo "✅ Valid algorithm: $algorithm"
            ;;
        *)
            echo "❌ Invalid algorithm: $algorithm"
            echo "Valid algorithms: mld, ch"
            exit 1
            ;;
    esac
done

# Check if Northern Zone OSM file exists
if [ ! -f "data/northern-zone-latest.osm.pbf" ]; then
    echo "❌ Northern Zone OSM file not found: data/northern-zone-latest.osm.pbf"
    echo "Please download it first."
    exit 1
fi

# Check file size
FILE_SIZE=$(ls -lh data/northern-zone-latest.osm.pbf | awk '{print $5}')
echo "📁 Northern Zone OSM file size: $FILE_SIZE"

# Check available memory
echo "💾 Checking system resources..."
docker system df

echo ""
echo "🔄 Starting Northern Zone Processing..."
echo "This will process ${#PROFILE_ARRAY[@]} profiles with ${#ALGORITHM_ARRAY[@]} algorithms"
echo "Total combinations: $((${#PROFILE_ARRAY[@]} * ${#ALGORITHM_ARRAY[@]}))"
echo ""

# Function to process a single profile-algorithm combination
process_combination() {
    local profile=$1
    local algorithm=$2
    local output_base="northern-zone-latest-${profile}-${algorithm}"
    
    echo ""
    echo "🚀 Processing: $profile profile with $algorithm algorithm"
    echo "=================================================="
    
    # Clean up any existing partial files for this combination
    echo "🧹 Cleaning up existing files for $profile-$algorithm..."
    rm -f "data/${output_base}.osrm"*
    
    # Step 1: Extract
    echo "📊 Step 1: Extracting road network with $profile profile..."
    docker run --rm -t \
        -v "${PWD}/data:/data" \
        -v "${PWD}/profiles:/profiles" \
        --memory=8g \
        --memory-swap=10g \
        --name "osrm-extract-northern-${profile}" \
        ghcr.io/project-osrm/osrm-backend \
        osrm-extract -p "/profiles/${profile}.lua" /data/northern-zone-latest.osm.pbf
    
    if [ $? -ne 0 ]; then
        echo "❌ Extract failed for $profile profile."
        return 1
    fi
    
    # Rename the extracted files to include profile and algorithm
    echo "🔄 Renaming extracted files..."
    for file in data/northern-zone-latest.osrm*; do
        if [ -f "$file" ]; then
            new_file="${file/northern-zone-latest.osrm/$output_base.osrm}"
            mv "$file" "$new_file"
            echo "   Renamed: $file -> $new_file"
        fi
    done
    
    # Step 2: Process based on algorithm
    if [ "$algorithm" = "mld" ]; then
        # MLD Algorithm: Partition and Customize
        echo "🔀 Step 2: Partitioning graph for MLD..."
        docker run --rm -t \
            -v "${PWD}/data:/data" \
            --memory=8g \
            --memory-swap=10g \
            --name "osrm-partition-northern-${profile}" \
            ghcr.io/project-osrm/osrm-backend \
            osrm-partition "/data/${output_base}.osrm"
        
        if [ $? -ne 0 ]; then
            echo "❌ Partition failed for $profile-$algorithm."
            return 1
        fi
        
        echo "⚙️  Step 3: Customizing for ${profile} profile with MLD..."
        docker run --rm -t \
            -v "${PWD}/data:/data" \
            --memory=8g \
            --memory-swap=10g \
            --name "osrm-customize-northern-${profile}" \
            ghcr.io/project-osrm/osrm-backend \
            osrm-customize "/data/${output_base}.osrm"
        
        if [ $? -ne 0 ]; then
            echo "❌ Customize failed for $profile-$algorithm."
            return 1
        fi
    else
        # CH Algorithm: Contract
        echo "🔀 Step 2: Contracting graph for CH..."
        docker run --rm -t \
            -v "${PWD}/data:/data" \
            --memory=8g \
            --memory-swap=10g \
            --name "osrm-contract-northern-${profile}" \
            ghcr.io/project-osrm/osrm-backend \
            osrm-contract "/data/${output_base}.osrm"
        
        if [ $? -ne 0 ]; then
            echo "❌ Contract failed for $profile-$algorithm."
            return 1
        fi
    fi
    
    echo "✅ Successfully processed: $profile profile with $algorithm algorithm"
    echo "📊 Generated files:"
    ls -lh "data/${output_base}.osrm"* | head -5
    echo ""
}

# Process all combinations
total_combinations=$((${#PROFILE_ARRAY[@]} * ${#ALGORITHM_ARRAY[@]}))
current_combination=0

for profile in "${PROFILE_ARRAY[@]}"; do
    for algorithm in "${ALGORITHM_ARRAY[@]}"; do
        current_combination=$((current_combination + 1))
        echo "🔄 Processing combination $current_combination of $total_combinations"
        
        if ! process_combination "$profile" "$algorithm"; then
            echo "❌ Failed to process $profile-$algorithm combination"
            echo "Continuing with next combination..."
            continue
        fi
    done
done

echo ""
echo "🎉 Northern Zone Multi-Profile Processing Complete!"
echo "=================================================="
echo ""
echo "📊 Summary of processed combinations:"
for profile in "${PROFILE_ARRAY[@]}"; do
    for algorithm in "${ALGORITHM_ARRAY[@]}"; do
        output_base="northern-zone-latest-${profile}-${algorithm}"
        if [ -f "data/${output_base}.osrm.properties" ]; then
            echo "✅ $profile-$algorithm: Ready"
        else
            echo "❌ $profile-$algorithm: Failed"
        fi
    done
done

echo ""
echo "🚀 Next steps:"
echo "1. Update docker-compose files to use the new processed data"
echo "2. Test the services with different profiles and algorithms"
echo "3. Update the demo script to showcase all combinations"
