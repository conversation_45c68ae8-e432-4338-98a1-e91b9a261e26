#!/bin/bash

# OSRM Multi-Profile Quick Start Script
# ====================================
#
# This script helps you quickly set up and test the OSRM multi-profile system
# for Northern India routing with multiple transportation modes and algorithms.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROFILES="car,bicycle"  # Start with car and bicycle (foot takes longer)
ALGORITHMS="mld,ch"
COMPOSE_FILE="docker-compose-multi-profile.yml"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose is installed"
    
    # Check jq
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. Installing jq for JSON processing..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install jq || print_error "Failed to install jq. Please install manually."
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y jq || print_error "Failed to install jq. Please install manually."
        fi
    fi
    print_success "jq is available"
    
    # Check bc
    if ! command -v bc &> /dev/null; then
        print_warning "bc is not installed. Installing bc for calculations..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install bc || print_error "Failed to install bc. Please install manually."
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update && sudo apt-get install -y bc || print_error "Failed to install bc. Please install manually."
        fi
    fi
    print_success "bc is available"
    
    # Check if northern zone data exists
    if [ ! -f "data/northern-zone-latest.osm.pbf" ]; then
        print_error "Northern zone OSM data not found: data/northern-zone-latest.osm.pbf"
        print_info "Please ensure the OSM data file is available before proceeding."
        exit 1
    fi
    print_success "Northern zone OSM data is available"
    
    echo ""
}

# Function to process data
process_data() {
    print_header "Processing OSRM Data"
    
    print_info "Processing profiles: $PROFILES"
    print_info "Processing algorithms: $ALGORITHMS"
    print_warning "This may take 30-60 minutes depending on your system..."
    echo ""
    
    if [ -x "./process-northern-zone-multi.sh" ]; then
        ./process-northern-zone-multi.sh "$PROFILES" "$ALGORITHMS"
    else
        print_error "Processing script not found or not executable: ./process-northern-zone-multi.sh"
        exit 1
    fi
    
    print_success "Data processing completed!"
    echo ""
}

# Function to start services
start_services() {
    print_header "Starting OSRM Services"
    
    # Stop any existing services
    print_info "Stopping any existing services..."
    docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
    
    # Start services
    print_info "Starting multi-profile OSRM services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service status
    print_info "Checking service status..."
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    print_success "Services started successfully!"
    echo ""
}

# Function to run tests
run_tests() {
    print_header "Running Demo Tests"
    
    print_info "Running comprehensive multi-profile demo..."
    
    if [ -x "./osrm-demo-routes.sh" ]; then
        ./osrm-demo-routes.sh all all
    else
        print_error "Demo script not found or not executable: ./osrm-demo-routes.sh"
        return 1
    fi
    
    print_success "Demo tests completed!"
    echo ""
}

# Function to display service information
show_service_info() {
    print_header "Service Information"
    
    echo -e "${CYAN}🚀 OSRM Multi-Profile Services:${NC}"
    echo ""
    echo -e "${GREEN}Car Services:${NC}"
    echo "  • Car MLD:     http://localhost:5000"
    echo "  • Car CH:      http://localhost:5001"
    echo ""
    echo -e "${GREEN}Bicycle Services:${NC}"
    echo "  • Bicycle MLD: http://localhost:5010"
    echo "  • Bicycle CH:  http://localhost:5011"
    echo ""
    echo -e "${GREEN}Additional Services:${NC}"
    echo "  • API Gateway: http://localhost:8080"
    echo "  • Frontend:    http://localhost:9966"
    echo ""
    
    echo -e "${CYAN}🧪 Quick Test Commands:${NC}"
    echo ""
    echo "# Test car routing (MLD)"
    echo "curl \"http://localhost:5000/route/v1/driving/77.209,28.6139;77.0266,28.4595\""
    echo ""
    echo "# Test bicycle routing (MLD)"
    echo "curl \"http://localhost:5010/route/v1/cycling/77.209,28.6139;77.0266,28.4595\""
    echo ""
    echo "# Test multi-waypoint optimization (CH)"
    echo "curl \"http://localhost:5001/trip/v1/driving/77.209,28.6139;77.0266,28.4595;76.7794,30.7333\""
    echo ""
    
    echo -e "${CYAN}📊 Demo Script Usage:${NC}"
    echo ""
    echo "# Run all tests"
    echo "./osrm-demo-routes.sh"
    echo ""
    echo "# Test specific mode and algorithm"
    echo "./osrm-demo-routes.sh car mld"
    echo "./osrm-demo-routes.sh bicycle ch"
    echo ""
    echo "# Test all modes with specific algorithm"
    echo "./osrm-demo-routes.sh all ch"
    echo ""
}

# Function to show menu
show_menu() {
    print_header "OSRM Multi-Profile Quick Start"
    
    echo "Please select an option:"
    echo ""
    echo "1) Full Setup (Check prerequisites + Process data + Start services + Run tests)"
    echo "2) Process Data Only"
    echo "3) Start Services Only"
    echo "4) Run Tests Only"
    echo "5) Show Service Information"
    echo "6) Stop Services"
    echo "7) Exit"
    echo ""
    read -p "Enter your choice (1-7): " choice
}

# Function to stop services
stop_services() {
    print_header "Stopping Services"
    
    docker-compose -f "$COMPOSE_FILE" down
    print_success "Services stopped successfully!"
    echo ""
}

# Main execution
main() {
    while true; do
        show_menu
        
        case $choice in
            1)
                check_prerequisites
                process_data
                start_services
                run_tests
                show_service_info
                ;;
            2)
                check_prerequisites
                process_data
                ;;
            3)
                start_services
                show_service_info
                ;;
            4)
                run_tests
                ;;
            5)
                show_service_info
                ;;
            6)
                stop_services
                ;;
            7)
                print_info "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please try again."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
        echo ""
    done
}

# Run main function
main "$@"
