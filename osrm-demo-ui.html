<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSRM Multi-Profile Demo - Northern India</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; 
            margin: 0; 
            display: flex; 
            height: 100vh; 
            background: #f8f9fa;
        }
        #sidebar { 
            width: 420px; 
            padding: 20px; 
            box-shadow: 2px 0 10px rgba(0,0,0,0.1); 
            overflow-y: auto; 
            background: white;
            border-right: 1px solid #e9ecef;
        }
        #map { 
            flex-grow: 1; 
        }
        h1 { 
            color: #2c3e50; 
            border-bottom: 3px solid #3498db; 
            padding-bottom: 10px; 
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        h2 { 
            color: #34495e; 
            font-size: 1.2em; 
            margin: 20px 0 10px 0;
        }
        .control-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: 600; 
            color: #495057;
        }
        select, button { 
            width: 100%; 
            padding: 12px; 
            border-radius: 8px; 
            border: 2px solid #dee2e6; 
            font-size: 14px; 
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        select:focus, button:focus {
            outline: none;
            border-color: #3498db;
        }
        button { 
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white; 
            cursor: pointer; 
            margin-top: 10px;
            font-weight: 600;
            border: none;
        }
        button:hover { 
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-1px);
        }
        button.transport-btn {
            margin: 5px 0;
            padding: 10px;
        }
        button.car-mode {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        button.car-mode:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
        }
        button.bicycle-mode {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        button.bicycle-mode:hover {
            background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
        }
        button.active {
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
        }
        button.optimize-btn {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        button.optimize-btn:hover {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
        }
        button.add-waypoint { 
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            margin-top: 5px; 
        }
        button.add-waypoint:hover { 
            background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
        }
        button.remove-waypoint { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            font-size: 12px; 
            padding: 5px 10px; 
            width: auto; 
            margin-left: 10px; 
        }
        .waypoint-item { 
            display: flex; 
            align-items: center; 
            margin-bottom: 10px; 
        }
        .waypoint-item select { 
            flex-grow: 1; 
        }
        .transport-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .algorithm-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 15px;
        }
        #results { 
            margin-top: 20px; 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 8px; 
            border: 1px solid #e9ecef; 
        }
        #results h2 { 
            margin-top: 0; 
            color: #2c3e50;
        }
        #results p { 
            margin: 8px 0; 
            font-size: 14px;
        }
        .status { 
            padding: 12px; 
            border-radius: 8px; 
            color: white; 
            text-align: center; 
            font-weight: 600; 
            margin-bottom: 15px;
        }
        .status.success { 
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        .status.error { 
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        .status.checking {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        .loader { 
            border: 4px solid #f3f3f4; 
            border-top: 4px solid #3498db; 
            border-radius: 50%; 
            width: 24px; 
            height: 24px; 
            animation: spin 1s linear infinite; 
            display: none; 
            margin: 15px auto; 
        }
        @keyframes spin { 
            0% { transform: rotate(0deg); } 
            100% { transform: rotate(360deg); } 
        }
        .comparison-results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        .result-card {
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            font-size: 13px;
        }
        .result-card h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 14px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
        }
        .metric-label {
            color: #6c757d;
        }
        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }
        hr {
            border: none;
            height: 1px;
            background: #e9ecef;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div id="sidebar">
        <h1>🇮🇳 OSRM Multi-Profile Demo</h1>
        
        <div id="status-container">
            <div id="status-light" class="status checking">🔄 Checking Services...</div>
        </div>

        <hr>

        <h2>🚗🚴 Transportation Mode</h2>
        <div class="transport-selection">
            <button id="car-btn" class="transport-btn car-mode active" data-mode="car">
                🚗 Car
            </button>
            <button id="bicycle-btn" class="transport-btn bicycle-mode" data-mode="bicycle">
                🚴 Bicycle
            </button>
        </div>

        <div class="algorithm-info" id="algorithm-info">
            <strong>🎯 Current Algorithm:</strong> <span id="current-algorithm">MLD (Fast Routing)</span><br>
            <span id="algorithm-description">Optimized for single point-to-point routing</span><br>
            <small id="algorithm-note">Note: CH trip optimization creates round trips and may show longer distances for linear routes</small>
        </div>

        <hr>

        <h2>📍 Route Waypoints</h2>
        <div id="waypoints-container">
            <label>Select locations for your route:</label>
        </div>
        <button id="add-waypoint" class="add-waypoint">+ Add Waypoint</button>

        <hr>

        <button id="optimize-trip" class="optimize-btn">
            🎯 Optimize Multi-Waypoint Trip (CH Algorithm)
        </button>
        
        <button id="calculate-route">📊 Calculate Route</button>
        
        <div id="loader" class="loader"></div>
        
        <div id="results" style="display: none;">
            <h2>📊 Route Results</h2>
            <div id="route-details"></div>
        </div>
    </div>

    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        const locations = {
            "Delhi": { lat: 28.6139, lon: 77.209 },
            "Gurgaon": { lat: 28.4595, lon: 77.0266 },
            "Chandigarh": { lat: 30.7333, lon: 76.7794 },
            "Amritsar": { lat: 31.6340, lon: 74.8723 },
            "Ludhiana": { lat: 30.9010, lon: 75.8573 },
            "Jaipur": { lat: 26.9124, lon: 75.7873 },
            "Agra": { lat: 27.1767, lon: 78.0081 },
            "Dehradun": { lat: 30.3165, lon: 78.0322 },
            "Haridwar": { lat: 29.9457, lon: 78.1642 },
            "Shimla": { lat: 31.1048, lon: 77.1734 },
            "Manali": { lat: 32.2432, lon: 77.1892 },
            "Panipat": { lat: 29.3909, lon: 76.9682 },
            "Ambala": { lat: 30.3752, lon: 76.7821 },
            "Karnal": { lat: 29.6857, lon: 76.9861 },
            "Sonipat": { lat: 28.9931, lon: 77.0151 },
            "Rohtak": { lat: 28.8955, lon: 76.5784 },
            "Hisar": { lat: 29.1492, lon: 75.7217 },
            "Bathinda": { lat: 30.2110, lon: 74.9400 }
        };

        const serviceUrls = {
            'car-mld': 'http://localhost:5000',
            'car-ch': 'http://localhost:5001',
            'bicycle-mld': 'http://localhost:5010',
            'bicycle-ch': 'http://localhost:5011'
        };

        let currentMode = 'car';
        let isOptimizeMode = false;
        let map, routeLayer, markers = [];

        // Initialize map
        map = L.map('map').setView([29.0, 77.0], 7);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // DOM elements
        const waypointsContainer = document.getElementById('waypoints-container');
        const addWaypointBtn = document.getElementById('add-waypoint');
        const calculateBtn = document.getElementById('calculate-route');
        const optimizeBtn = document.getElementById('optimize-trip');
        const resultsDiv = document.getElementById('results');
        const loader = document.getElementById('loader');
        const carBtn = document.getElementById('car-btn');
        const bicycleBtn = document.getElementById('bicycle-btn');

        // Transport mode selection
        carBtn.addEventListener('click', () => setTransportMode('car'));
        bicycleBtn.addEventListener('click', () => setTransportMode('bicycle'));

        function setTransportMode(mode) {
            currentMode = mode;
            
            // Update button states
            document.querySelectorAll('.transport-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`${mode}-btn`).classList.add('active');
            
            updateAlgorithmInfo();
        }

        function updateAlgorithmInfo() {
            const algorithmSpan = document.getElementById('current-algorithm');
            const descriptionSpan = document.getElementById('algorithm-description');
            const noteSpan = document.getElementById('algorithm-note');

            if (isOptimizeMode) {
                algorithmSpan.textContent = 'CH (Contraction Hierarchies)';
                descriptionSpan.textContent = 'Optimized for multi-waypoint trip planning and route optimization';
                noteSpan.style.display = 'block';
            } else {
                algorithmSpan.textContent = 'MLD (Multi-Level Dijkstra)';
                descriptionSpan.textContent = 'Fast single point-to-point routing';
                noteSpan.style.display = 'none';
            }
        }

        // Optimize mode toggle
        optimizeBtn.addEventListener('click', () => {
            isOptimizeMode = !isOptimizeMode;
            optimizeBtn.textContent = isOptimizeMode ? 
                '⚡ Use Fast Routing (MLD Algorithm)' : 
                '🎯 Optimize Multi-Waypoint Trip (CH Algorithm)';
            optimizeBtn.style.background = isOptimizeMode ?
                'linear-gradient(135deg, #3498db 0%, #2980b9 100%)' :
                'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)';
            updateAlgorithmInfo();
        });

        function createWaypointSelect(defaultValue) {
            const select = document.createElement('select');
            select.className = 'waypoint-select';
            
            // Add empty option
            const emptyOption = new Option('-- Select Location --', '');
            select.add(emptyOption);
            
            for (const location in locations) {
                const option = new Option(location, location);
                select.add(option);
            }
            if (defaultValue) {
                select.value = defaultValue;
            }
            return select;
        }

        function addWaypoint(defaultValue) {
            const waypointItem = document.createElement('div');
            waypointItem.className = 'waypoint-item';

            const select = createWaypointSelect(defaultValue);
            waypointItem.appendChild(select);

            const removeBtn = document.createElement('button');
            removeBtn.textContent = '×';
            removeBtn.className = 'remove-waypoint';
            removeBtn.onclick = () => {
                if (waypointsContainer.querySelectorAll('.waypoint-item').length > 2) {
                    waypointItem.remove();
                }
            };
            waypointItem.appendChild(removeBtn);
            waypointsContainer.appendChild(waypointItem);
        }

        addWaypointBtn.addEventListener('click', () => addWaypoint());

        function formatDuration(seconds) {
            const h = Math.floor(seconds / 3600);
            const m = Math.floor((seconds % 3600) / 60);
            let str = "";
            if (h > 0) str += `${h}h `;
            if (m > 0) str += `${m}m`;
            return str.trim() || "< 1m";
        }

        async function checkServiceStatus() {
            const statusLight = document.getElementById('status-light');
            const services = [`${currentMode}-mld`, `${currentMode}-ch`];
            let workingServices = 0;
            
            for (const service of services) {
                try {
                    const response = await fetch(serviceUrls[service], { 
                        signal: AbortSignal.timeout(3000) 
                    });
                    if (response.ok) workingServices++;
                } catch (error) {
                    // Service not available
                }
            }
            
            if (workingServices === services.length) {
                statusLight.className = 'status success';
                statusLight.textContent = `✅ All ${currentMode.toUpperCase()} services online`;
            } else if (workingServices > 0) {
                statusLight.className = 'status checking';
                statusLight.textContent = `⚠️ Some ${currentMode.toUpperCase()} services offline`;
            } else {
                statusLight.className = 'status error';
                statusLight.textContent = `❌ ${currentMode.toUpperCase()} services offline`;
            }
        }

        calculateBtn.addEventListener('click', async () => {
            const waypointSelects = waypointsContainer.querySelectorAll('.waypoint-select');
            const selectedWaypoints = Array.from(waypointSelects)
                .map(select => select.value)
                .filter(value => value !== '');

            if (selectedWaypoints.length < 2) {
                alert("Please select at least 2 locations for your route.");
                return;
            }

            const coords = selectedWaypoints.map(name => {
                const loc = locations[name];
                return `${loc.lon},${loc.lat}`;
            });

            const algorithm = isOptimizeMode ? 'ch' : 'mld';
            const serviceKey = `${currentMode}-${algorithm}`;
            const baseUrl = serviceUrls[serviceKey];

            const profileName = currentMode === 'car' ? 'driving' : 'cycling';
            const endpoint = isOptimizeMode ? 'trip' : 'route';
            let url = `${baseUrl}/${endpoint}/v1/${profileName}/${coords.join(';')}?overview=full&geometries=geojson`;

            // For trip optimization, add source=first&destination=last to prevent round trips
            if (isOptimizeMode && selectedWaypoints.length > 2) {
                url += '&source=first&destination=last';
            }

            loader.style.display = 'block';
            resultsDiv.style.display = 'none';

            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

                const data = await response.json();
                if (data.code !== 'Ok') throw new Error(`OSRM API Error: ${data.message || data.code}`);

                const route = isOptimizeMode ? data.trips[0] : data.routes[0];
                displayResults(route, selectedWaypoints, algorithm, isOptimizeMode);

                // Clear previous route
                if (routeLayer) {
                    map.removeLayer(routeLayer);
                }
                markers.forEach(marker => map.removeLayer(marker));
                markers = [];

                // Add route line to map
                if (route.geometry && route.geometry.coordinates) {
                    const routeCoords = route.geometry.coordinates.map(coord => [coord[1], coord[0]]);
                    routeLayer = L.polyline(routeCoords, {
                        color: currentMode === 'car' ? '#e74c3c' : '#27ae60',
                        weight: 4,
                        opacity: 0.8
                    }).addTo(map);
                } else if (route.geometry && typeof route.geometry === 'string') {
                    // Handle polyline encoded geometry
                    try {
                        const decoded = L.Polyline.fromEncoded(route.geometry);
                        routeLayer = L.polyline(decoded.getLatLngs(), {
                            color: currentMode === 'car' ? '#e74c3c' : '#27ae60',
                            weight: 4,
                            opacity: 0.8
                        }).addTo(map);
                    } catch (e) {
                        console.log('Could not decode route geometry');
                    }
                }

                // Add markers for waypoints
                selectedWaypoints.forEach((name, index) => {
                    const loc = locations[name];
                    const marker = L.marker([loc.lat, loc.lon])
                        .bindPopup(`${index + 1}. ${name}`)
                        .addTo(map);
                    markers.push(marker);
                });

                // Fit map to show route or markers
                if (routeLayer) {
                    map.fitBounds(routeLayer.getBounds().pad(0.1));
                } else if (markers.length > 0) {
                    const group = new L.featureGroup(markers);
                    map.fitBounds(group.getBounds().pad(0.1));
                }

            } catch (error) {
                alert(`Failed to calculate route: ${error.message}`);
            } finally {
                loader.style.display = 'none';
            }
        });

        function displayResults(route, waypoints, algorithm, isOptimized = false) {
            const distanceKm = (route.distance / 1000).toFixed(2);
            const durationStr = formatDuration(route.duration);
            const speedKmh = route.duration > 0 ? (route.distance / 1000 / (route.duration / 3600)).toFixed(1) : "0";

            const modeIcon = currentMode === 'car' ? '🚗' : '🚴';
            const algorithmIcon = algorithm === 'mld' ? '⚡' : '🎯';
            const optimizedText = isOptimized ? ' (Trip Optimized)' : '';

            let routeDescription = waypoints.join(' → ');
            if (isOptimized && waypoints.length > 2) {
                routeDescription += ' (Optimized Order)';
            }

            document.getElementById('route-details').innerHTML = `
                <div class="result-card">
                    <h4>${modeIcon} ${currentMode.toUpperCase()} Route ${algorithmIcon} ${algorithm.toUpperCase()}${optimizedText}</h4>
                    <div class="metric">
                        <span class="metric-label">Distance:</span>
                        <span class="metric-value">${distanceKm} km</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Duration:</span>
                        <span class="metric-value">${durationStr}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Avg Speed:</span>
                        <span class="metric-value">${speedKmh} km/h</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Route:</span>
                        <span class="metric-value">${routeDescription}</span>
                    </div>
                    ${isOptimized ? `
                    <div class="metric">
                        <span class="metric-label">Algorithm:</span>
                        <span class="metric-value">CH (Contraction Hierarchies) for trip optimization</span>
                    </div>
                    ` : ''}
                </div>
            `;

            resultsDiv.style.display = 'block';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            addWaypoint("Delhi");
            addWaypoint("Gurgaon");
            checkServiceStatus();
            setInterval(checkServiceStatus, 30000); // Check every 30 seconds
        });
    </script>
</body>
</html>
