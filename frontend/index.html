<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSRM Route Visualizer</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css" />
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; margin: 0; display: flex; height: 100vh; }
        #sidebar { width: 400px; padding: 20px; box-shadow: 2px 0 5px rgba(0,0,0,0.1); overflow-y: auto; background: #f8f9fa; }
        #map { flex-grow: 1; }
        h1, h2 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .control-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, button { width: 100%; padding: 10px; border-radius: 5px; border: 1px solid #ccc; font-size: 16px; box-sizing: border-box; }
        button { background-color: #007bff; color: white; cursor: pointer; margin-top: 10px; }
        button:hover { background-color: #0056b3; }
        button.add-waypoint { background-color: #28a745; margin-top: 5px; }
        button.add-waypoint:hover { background-color: #218838; }
        button.remove-waypoint { background-color: #dc3545; font-size: 12px; padding: 5px 10px; width: auto; margin-left: 10px; }
        .waypoint-item { display: flex; align-items: center; margin-bottom: 10px; }
        .waypoint-item select { flex-grow: 1; }
        #results { margin-top: 20px; background: #fff; padding: 15px; border-radius: 5px; border: 1px solid #eee; }
        #results h2 { margin-top: 0; }
        #results p { margin: 5px 0; }
        .status { padding: 10px; border-radius: 5px; color: white; text-align: center; font-weight: bold; }
        .status.success { background-color: #28a745; }
        .status.error { background-color: #dc3545; }
        .loader { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 20px; height: 20px; animation: spin 2s linear infinite; display: none; margin: 10px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .optimize-group { display: flex; align-items: center; margin-top: 10px; }
        .optimize-group input { margin-right: 10px; }
    </style>
</head>
<body>

    <div id="sidebar">
        <h1>OSRM Visualizer</h1>
        <div id="status-container">
            <p>Service Status: <span id="status-indicator">Checking...</span></p>
            <div id="status-light" class="status"></div>
        </div>
        <hr>
        <div id="waypoints-container">
            <label>Route Waypoints:</label>
        </div>
        <button id="add-waypoint" class="add-waypoint">+ Add Waypoint</button>
        <hr>
        <div class="control-group">
            <label for="profile-select">Select Profile:</label>
            <select id="profile-select">
                <option value="car">Car (CH)</option>
                <option value="bicycle">Bicycle (CH)</option>
            </select>
        </div>
        <div class="optimize-group">
            <input type="checkbox" id="optimize-trip">
            <label for="optimize-trip">Optimize Route (Find shortest path)</label>
        </div>
        <hr>
        <button id="calculate-route">Calculate Route</button>
        <div id="loader" class="loader"></div>
        <div id="results" style="display: none;">
            <h2>Route Details</h2>
            <p><strong>Distance:</strong> <span id="distance"></span></p>
            <p><strong>Duration:</strong> <span id="duration"></span></p>
            <p><strong>Avg Speed:</strong> <span id="speed"></span></p>
        </div>
    </div>

    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.min.js"></script>
    <script>
        const locations = {
            "Delhi": { lat: 28.6139, lon: 77.209 },
            "Gurgaon": { lat: 28.4595, lon: 77.0266 },
            "Chandigarh": { lat: 30.7333, lon: 76.7794 },
            "Amritsar": { lat: 31.6340, lon: 74.8723 },
            "Ludhiana": { lat: 30.9010, lon: 75.8573 },
            "Jaipur": { lat: 26.9124, lon: 75.7873 },
            "Agra": { lat: 27.1767, lon: 78.0081 },
            "Dehradun": { lat: 30.3165, lon: 78.0322 },
            "Haridwar": { lat: 29.9457, lon: 78.1642 },
            "Shimla": { lat: 31.1048, lon: 77.1734 },
            "Manali": { lat: 32.2432, lon: 77.1892 },
            "Jammu": { lat: 32.7266, lon: 74.8570 },
            "Srinagar": { lat: 34.0837, lon: 74.7973 },
            "Panipat": { lat: 29.3909, lon: 76.9682 },
            "Ambala": { lat: 30.3752, lon: 76.7821 },
            "Karnal": { lat: 29.6857, lon: 76.9861 },
            "Sonipat": { lat: 28.9931, lon: 77.0151 },
            "Rohtak": { lat: 28.8955, lon: 76.5784 },
            "Hisar": { lat: 29.1492, lon: 75.7217 },
            "Bathinda": { lat: 30.2110, lon: 74.9400 }
        };

        const OSRM_BASE_URL = "http://localhost";

        const OSRM_PORTS = {
            "car": {
                "route": 5000, // car-mld
                "trip": 5001   // car-ch
            },
            "bicycle": {
                "route": 5010, // bicycle-mld
                "trip": 5011   // bicycle-ch
            }
        };

        const waypointsContainer = document.getElementById('waypoints-container');
        const addWaypointBtn = document.getElementById('add-waypoint');
        const calculateBtn = document.getElementById('calculate-route');
        const profileSelect = document.getElementById('profile-select');
        const optimizeCheckbox = document.getElementById('optimize-trip');
        const resultsDiv = document.getElementById('results');
        const loader = document.getElementById('loader');

        const map = L.map('map').setView([29.0, 77.0], 7);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        let routingControl = null;

        function createWaypointSelect(defaultValue) {
            const select = document.createElement('select');
            select.className = 'waypoint-select';
            for (const location in locations) {
                const option = new Option(location, location);
                select.add(option);
            }
            if (defaultValue) {
                select.value = defaultValue;
            }
            return select;
        }

        function addWaypoint(defaultValue) {
            const waypointItem = document.createElement('div');
            waypointItem.className = 'waypoint-item';

            const select = createWaypointSelect(defaultValue);
            waypointItem.appendChild(select);

            const removeBtn = document.createElement('button');
            removeBtn.textContent = 'Remove';
            removeBtn.className = 'remove-waypoint';
            removeBtn.onclick = () => {
                if (waypointsContainer.querySelectorAll('.waypoint-item').length > 2) {
                    waypointItem.remove();
                }
            };
            waypointItem.appendChild(removeBtn);
            waypointsContainer.appendChild(waypointItem);
        }

        addWaypointBtn.addEventListener('click', () => addWaypoint());

        function formatDuration(seconds) {
            const h = Math.floor(seconds / 3600);
            const m = Math.floor((seconds % 3600) / 60);
            const s = Math.round(seconds % 60);
            let str = "";
            if (h > 0) str += `${h}h `;
            if (m > 0) str += `${m}m `;
            if (s > 0 || str === "") str += `${s}s`;
            return str.trim();
        }

        // Simple Polyline Decoder (Google Encoded Polyline Algorithm Format)
        function decodePolyline(encoded) {
            var len = encoded.length;
            var index = 0;
            var lat = 0;
            var lng = 0;
            var array = [];

            while (index < len) {
                var b;
                var shift = 0;
                var result = 0;
                do {
                    b = encoded.charCodeAt(index++) - 63;
                    result |= (b & 0x1f) << shift;
                    shift += 5;
                } while (b >= 0x20);
                var dlat = ((result & 1) ? ~(result >> 1) : (result >> 1));
                lat += dlat;

                shift = 0;
                result = 0;
                do {
                    b = encoded.charCodeAt(index++) - 63;
                    result |= (b & 0x1f) << shift;
                    shift += 5;
                } while (b >= 0x20);
                var dlng = ((result & 1) ? ~(result >> 1) : (result >> 1));
                lng += dlng;

                array.push([lat / 1e5, lng / 1e5]);
            }
            return array;
        }

        async function checkServiceStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusLight = document.getElementById('status-light');
            try {
                await fetch(OSRM_BASE_URL, { signal: AbortSignal.timeout(3000) });
                statusIndicator.textContent = "Online";
                statusLight.className = 'status success';
                statusLight.textContent = '✅ Running';
            } catch (error) {
                statusIndicator.textContent = "Offline";
                statusLight.className = 'status error';
                statusLight.textContent = '❌ Not Reachable';
            }
        }

        calculateBtn.addEventListener('click', async () => {
            const waypointSelects = waypointsContainer.querySelectorAll('.waypoint-select');
            const originalWaypoints = Array.from(waypointSelects).map(select => ({ name: select.value, ...locations[select.value] }));
            const coords = originalWaypoints.map(wp => `${wp.lon},${wp.lat}`);

            if (coords.length < 2) {
                alert("Please select at least a start and end point.");
                return;
            }

            const selectedProfile = profileSelect.value;
            const isOptimized = optimizeCheckbox.checked && coords.length > 2;
            const endpoint = isOptimized ? 'trip/v1/' + selectedProfile + '/' : 'route/v1/' + selectedProfile + '/';
            const params = isOptimized ? '?roundtrip=false' : '';
            const url = `${OSRM_BASE_URL}/${endpoint}${coords.join(';')}${params}`;

            loader.style.display = 'block';
            resultsDiv.style.display = 'none';

            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                if (data.code !== 'Ok') throw new Error(`OSRM API Error: ${data.message || data.code}`);

                const route = isOptimized ? data.trips[0] : data.routes[0];
                const distanceKm = (route.distance / 1000).toFixed(2);
                const durationStr = formatDuration(route.duration);
                const speedKmh = (route.distance > 0 && route.duration > 0) ? (distanceKm / (route.duration / 3600)).toFixed(1) : "0";

                document.getElementById('distance').textContent = `${distanceKm} km`;
                document.getElementById('duration').textContent = durationStr;
                document.getElementById('speed').textContent = `${speedKmh} km/h`;
                resultsDiv.style.display = 'block';

                // Clear previous routing control or polyline
                if (routingControl) {
                    map.removeControl(routingControl);
                    routingControl = null;
                }
                // Also remove any directly drawn polylines
                map.eachLayer(function(layer) {
                    if (layer instanceof L.Polyline && layer.options.isOptimizedRoute) {
                        map.removeLayer(layer);
                    }
                    if (layer instanceof L.Marker && layer.options.isOptimizedMarker) {
                        map.removeLayer(layer);
                    }
                });

                if (isOptimized) {
                    // Reorder waypoints based on the trip response's waypoint_index
                    const newOrderedOriginalWaypoints = data.waypoints.map(wp => originalWaypoints[wp.waypoint_index]);
                    const newOrderNames = newOrderedOriginalWaypoints.map(wp => wp.name);

                    // Update the UI dropdowns with the correct friendly names in the new order.
                    waypointsContainer.innerHTML = '<label>Route Waypoints:</label>'; // Clear existing
                    newOrderNames.forEach(name => addWaypoint(name));

                    // Decode the optimized route geometry and draw it directly
                    const routeCoords = decodePolyline(route.geometry);
                    const polyline = L.polyline(routeCoords.map(c => [c[0], c[1]]), { color: 'blue', weight: 5, opacity: 0.7, isOptimizedRoute: true }).addTo(map);
                    map.fitBounds(polyline.getBounds());

                    // Add markers for the optimized waypoints
                    data.waypoints.forEach(wp => {
                        L.marker([wp.location[1], wp.location[0]], { isOptimizedMarker: true }).addTo(map);
                    });

                } else {
                    const leafletWaypoints = originalWaypoints.map(wp => L.latLng(wp.lat, wp.lon));
                    routingControl = L.Routing.control({
                        waypoints: leafletWaypoints,
                        routeWhileDragging: false,
                        show: false,
                        addWaypoints: false,
                        createMarker: function() { return null; }
                    }).addTo(map);
                }

            } catch (error) {
                alert(`Failed to calculate route: ${error.message}`);
            } finally {
                loader.style.display = 'none';
            }
        });
</body>
</html>