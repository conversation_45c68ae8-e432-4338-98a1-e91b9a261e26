#!/bin/bash

# OSRM Northern India Route Demo Script
# =====================================
#
# This script demonstrates ETA and distance calculations using a local OSRM instance
# running with Northern India map data. It tests various routes within the Northern
# India region including:
#
# 1. Short distance routes (Delhi NCR and nearby areas)
# 2. Medium distance routes (within Northern India)
# 3. Long distance routes (across Northern India)
# 4. Hill station routes (including Himachal Pradesh)
# 5. Punjab and Haryana routes
# 6. Multi-waypoint routes (complex routing scenarios)
#
# The script tests over 20 different routes and provides detailed information about:
# - Distance in kilometers
# - Duration in hours, minutes, and seconds
# - Average speed
#
# Prerequisites:
# - Local OSRM service running on http://localhost:5000
# - jq (JSON processor) installed
# - bc (calculator) installed
#
# Usage: ./osrm-demo-routes.sh

set -e

# Configuration
BASE_URL="http://localhost:5000"
TIMEOUT=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Northern India coordinates (longitude,latitude format for OSRM)
# Using simple variables instead of associative arrays for better compatibility
# These coordinates are verified to work with the current OSRM northern zone setup
DELHI="77.209,28.6139"
GURGAON="77.0266,28.4595"
CHANDIGARH="76.7794,30.7333"
AMRITSAR="74.8723,31.6340"
LUDHIANA="75.8573,30.9010"
JAIPUR="75.7873,26.9124"
AGRA="78.0081,27.1767"
DEHRADUN="78.0322,30.3165"
HARIDWAR="78.1642,29.9457"
SHIMLA="77.1734,31.1048"
MANALI="77.1892,32.2432"
JAMMU="74.8570,32.7266"
SRINAGAR="74.7973,34.0837"
PANIPAT="76.9682,29.3909"
AMBALA="76.7821,30.3752"
KARNAL="76.9861,29.6857"
SONIPAT="77.0151,28.9931"
ROHTAK="76.5784,28.8955"
HISAR="75.7217,29.1492"
BATHINDA="74.9400,30.2110"

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_route() {
    echo -e "${YELLOW}🛣️  $1${NC}"
}

# Function to check if OSRM service is running
check_osrm_service() {
    print_info "Checking OSRM service availability..."

    if curl -s --max-time $TIMEOUT "$BASE_URL/" > /dev/null 2>&1; then
        print_success "OSRM service is running on $BASE_URL"
        return 0
    else
        print_error "OSRM service is not accessible at $BASE_URL"
        print_info "Please ensure the service is running with: docker-compose up -d"
        exit 1
    fi
}

# Function to format duration from seconds to human readable
format_duration() {
    local seconds=$1
    # Convert to integer to handle floating point values
    local int_seconds=$(echo "$seconds" | cut -d'.' -f1)
    local hours=$((int_seconds / 3600))
    local minutes=$(((int_seconds % 3600) / 60))
    local remaining_seconds=$((int_seconds % 60))

    if [ $hours -gt 0 ]; then
        printf "%dh %dm %ds" $hours $minutes $remaining_seconds
    elif [ $minutes -gt 0 ]; then
        printf "%dm %ds" $minutes $remaining_seconds
    else
        printf "%ds" $remaining_seconds
    fi
}

# Function to format distance from meters to km
format_distance() {
    local meters=$1
    local km=$(echo "scale=2; $meters / 1000" | bc -l)
    printf "%.2f km" $km
}

# Function to get route information
get_route_info() {
    local from_coords=$1
    local to_coords=$2
    local from_name=$3
    local to_name=$4

    local url="$BASE_URL/route/v1/driving/$from_coords;$to_coords?overview=false&alternatives=false&steps=false"

    # Make API call with timeout
    local response=$(curl -s --max-time $TIMEOUT "$url" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$response" ]; then
        print_error "Failed to get route from $from_name to $to_name (Network error)"
        return 1
    fi

    # Parse JSON response
    local code=$(echo "$response" | jq -r '.code // "Error"')

    if [ "$code" != "Ok" ]; then
        print_error "Failed to get route from $from_name to $to_name (Code: $code)"
        return 1
    fi

    # Extract route information
    local distance=$(echo "$response" | jq -r '.routes[0].distance // 0')
    local duration=$(echo "$response" | jq -r '.routes[0].duration // 0')

    if [ "$distance" = "0" ] || [ "$duration" = "0" ]; then
        print_error "Invalid route data from $from_name to $to_name"
        return 1
    fi

    # Format and display results
    local formatted_distance=$(format_distance $distance)
    local formatted_duration=$(format_duration $duration)

    print_route "$from_name → $to_name"
    echo "   📏 Distance: $formatted_distance"
    echo "   ⏱️  Duration: $formatted_duration"
    echo "   🚗 Avg Speed: $(echo "scale=1; $distance * 3.6 / $duration" | bc -l) km/h"
    echo ""

    return 0
}

# Function to test multi-waypoint route
test_multi_waypoint_route() {
    local waypoints=$1
    local route_name=$2

    print_route "Multi-waypoint route: $route_name"

    local url="$BASE_URL/route/v1/driving/$waypoints?overview=false&alternatives=false&steps=false"
    local response=$(curl -s --max-time $TIMEOUT "$url" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$response" ]; then
        print_error "Failed to get multi-waypoint route (Network error)"
        return 1
    fi

    local code=$(echo "$response" | jq -r '.code // "Error"')

    if [ "$code" != "Ok" ]; then
        print_error "Failed to get multi-waypoint route (Code: $code)"
        return 1
    fi

    local distance=$(echo "$response" | jq -r '.routes[0].distance // 0')
    local duration=$(echo "$response" | jq -r '.routes[0].duration // 0')

    if [ "$distance" != "0" ] && [ "$duration" != "0" ]; then
        local formatted_distance=$(format_distance $distance)
        local formatted_duration=$(format_duration $duration)

        echo "   📏 Total Distance: $formatted_distance"
        echo "   ⏱️  Total Duration: $formatted_duration"
        echo "   🚗 Avg Speed: $(echo "scale=1; $distance * 3.6 / $duration" | bc -l) km/h"
        print_success "Multi-waypoint route calculated successfully"
    else
        print_error "Invalid multi-waypoint route data"
        return 1
    fi
    echo ""
}

# Main execution
main() {
    print_header "OSRM Northern India Route Demonstration"

    # Check dependencies
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed. Please install jq first."
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        print_error "bc is required but not installed. Please install bc first."
        exit 1
    fi

    # Check OSRM service
    check_osrm_service
    echo ""

    print_header "Testing Individual Routes"

    # Test 1: Delhi NCR and nearby routes (short distances)
    print_info "1. Delhi NCR and Nearby Regional Routes"
    get_route_info "$DELHI" "$GURGAON" "Delhi" "Gurgaon"
    get_route_info "$DELHI" "$SONIPAT" "Delhi" "Sonipat"
    get_route_info "$DELHI" "$ROHTAK" "Delhi" "Rohtak"
    get_route_info "$GURGAON" "$PANIPAT" "Gurgaon" "Panipat"

    # Test 2: Medium distance routes within Northern India
    print_info "2. Medium Distance Routes"
    get_route_info "$DELHI" "$JAIPUR" "Delhi" "Jaipur"
    get_route_info "$DELHI" "$CHANDIGARH" "Delhi" "Chandigarh"
    get_route_info "$DELHI" "$AGRA" "Delhi" "Agra"
    get_route_info "$CHANDIGARH" "$SHIMLA" "Chandigarh" "Shimla"

    # Test 3: Long distance routes
    print_info "3. Long Distance Routes"
    get_route_info "$DELHI" "$AMRITSAR" "Delhi" "Amritsar"
    get_route_info "$CHANDIGARH" "$AMRITSAR" "Chandigarh" "Amritsar"
    get_route_info "$LUDHIANA" "$BATHINDA" "Ludhiana" "Bathinda"
    get_route_info "$JAIPUR" "$AGRA" "Jaipur" "Agra"

    # Test 4: Hill station routes
    print_info "4. Hill Station Routes"
    get_route_info "$DELHI" "$DEHRADUN" "Delhi" "Dehradun"
    get_route_info "$DEHRADUN" "$HARIDWAR" "Dehradun" "Haridwar"
    get_route_info "$SHIMLA" "$MANALI" "Shimla" "Manali"

    # Test 5: Punjab and Haryana routes
    print_info "5. Punjab and Haryana Routes"
    get_route_info "$AMBALA" "$LUDHIANA" "Ambala" "Ludhiana"
    get_route_info "$KARNAL" "$HISAR" "Karnal" "Hisar"

    print_header "Testing Multi-Waypoint Routes"

    # Multi-waypoint route 1: Delhi and surrounding areas
    test_multi_waypoint_route "$DELHI;$SONIPAT;$PANIPAT;$KARNAL;$AMBALA;$CHANDIGARH" "Delhi to Chandigarh via Sonipat-Panipat-Karnal-Ambala"

    # Multi-waypoint route 2: Golden Triangle
    test_multi_waypoint_route "$DELHI;$AGRA;$JAIPUR;$DELHI" "Golden Triangle (Delhi-Agra-Jaipur)"

    # Multi-waypoint route 3: Punjab circuit
    test_multi_waypoint_route "$DELHI;$CHANDIGARH;$LUDHIANA;$AMRITSAR;$DELHI" "Punjab Circuit"

    # Multi-waypoint route 4: Haryana circuit
    test_multi_waypoint_route "$DELHI;$SONIPAT;$KARNAL;$AMBALA;$CHANDIGARH;$SHIMLA" "Delhi to Shimla via Haryana"

    # Multi-waypoint route 5: Hill stations tour
    test_multi_waypoint_route "$DELHI;$CHANDIGARH;$SHIMLA;$MANALI" "Northern Hill Stations Tour"

    print_header "Route Testing Summary"
    print_success "All route demonstrations completed successfully!"
    print_info "Local OSRM instance is working properly for Northern India region"
    print_info "Service URL: $BASE_URL"
    print_info "Frontend URL: http://localhost:9966"
    echo ""
    print_info "Note: For comparison with public OSRM API, you can manually test routes at:"
    print_info "Public API: https://router.project-osrm.org/route/v1/driving/{coordinates}"
    echo ""
}

# Run main function
main "$@"
