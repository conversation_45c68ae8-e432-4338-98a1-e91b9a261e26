#!/bin/bash

# OSRM Northern India Multi-Profile Route Demo Script
# ==================================================
#
# This script demonstrates ETA and distance calculations using local OSRM instances
# running with Northern India map data. It tests various routes within the Northern
# India region with multiple transportation modes and algorithms:
#
# Transportation Modes:
# 1. Car/Driving (default)
# 2. Bicycle/Cycling
# 3. Walking/Foot
#
# Algorithms:
# 1. MLD (Multi-Level Dijkstra) - Fast for single routes
# 2. CH (Contraction Hierarchies) - Optimized for multi-waypoint trips
#
# Route Types Tested:
# 1. Short distance routes (Delhi NCR and nearby areas)
# 2. Medium distance routes (within Northern India)
# 3. Long distance routes (across Northern India)
# 4. Hill station routes (including Himachal Pradesh)
# 5. Punjab and Haryana routes
# 6. Multi-waypoint routes (complex routing scenarios with CH optimization)
# 7. Multi-modal comparisons (same route with different transportation modes)
#
# The script provides detailed information about:
# - Distance in kilometers
# - Duration in hours, minutes, and seconds
# - Average speed for each transportation mode
# - Algorithm performance comparison
#
# Prerequisites:
# - Local OSRM services running on multiple ports (see service configuration)
# - jq (JSON processor) installed
# - bc (calculator) installed
#
# Usage: ./osrm-demo-routes.sh [mode] [algorithm]
#   mode: car, bicycle, foot, all (default: all)
#   algorithm: mld, ch, all (default: all)

set -e

# Service Configuration
declare -A SERVICE_URLS
SERVICE_URLS[car-mld]="http://localhost:5000"
SERVICE_URLS[car-ch]="http://localhost:5001"
SERVICE_URLS[bicycle-mld]="http://localhost:5010"
SERVICE_URLS[bicycle-ch]="http://localhost:5011"
SERVICE_URLS[foot-mld]="http://localhost:5020"
SERVICE_URLS[foot-ch]="http://localhost:5021"

# Gateway URL (if using nginx gateway)
GATEWAY_URL="http://localhost:8080"

# Configuration
TIMEOUT=15
MODE=${1:-all}
ALGORITHM=${2:-all}

# Validate input parameters
case $MODE in
    car|bicycle|foot|all)
        echo "✅ Mode: $MODE"
        ;;
    *)
        echo "❌ Invalid mode: $MODE"
        echo "Valid modes: car, bicycle, foot, all"
        exit 1
        ;;
esac

case $ALGORITHM in
    mld|ch|all)
        echo "✅ Algorithm: $ALGORITHM"
        ;;
    *)
        echo "❌ Invalid algorithm: $ALGORITHM"
        echo "Valid algorithms: mld, ch, all"
        exit 1
        ;;
esac

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
MAGENTA='\033[0;95m'
NC='\033[0m' # No Color

# Northern India coordinates (longitude,latitude format for OSRM)
# Using simple variables instead of associative arrays for better compatibility
# These coordinates are verified to work with the current OSRM northern zone setup
DELHI="77.209,28.6139"
GURGAON="77.0266,28.4595"
CHANDIGARH="76.7794,30.7333"
AMRITSAR="74.8723,31.6340"
LUDHIANA="75.8573,30.9010"
JAIPUR="75.7873,26.9124"
AGRA="78.0081,27.1767"
DEHRADUN="78.0322,30.3165"
HARIDWAR="78.1642,29.9457"
SHIMLA="77.1734,31.1048"
MANALI="77.1892,32.2432"
JAMMU="74.8570,32.7266"
SRINAGAR="74.7973,34.0837"
PANIPAT="76.9682,29.3909"
AMBALA="76.7821,30.3752"
KARNAL="76.9861,29.6857"
SONIPAT="77.0151,28.9931"
ROHTAK="76.5784,28.8955"
HISAR="75.7217,29.1492"
BATHINDA="74.9400,30.2110"

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_route() {
    echo -e "${YELLOW}🛣️  $1${NC}"
}

print_mode() {
    case $1 in
        car) echo -e "${BLUE}🚗 $2${NC}" ;;
        bicycle) echo -e "${GREEN}🚴 $2${NC}" ;;
        foot) echo -e "${PURPLE}🚶 $2${NC}" ;;
        *) echo -e "${CYAN}🔧 $2${NC}" ;;
    esac
}

print_algorithm() {
    case $1 in
        mld) echo -e "${CYAN}⚡ MLD: $2${NC}" ;;
        ch) echo -e "${MAGENTA}🎯 CH: $2${NC}" ;;
        *) echo -e "${YELLOW}🔧 $2${NC}" ;;
    esac
}

# Function to check if OSRM service is running
check_osrm_service() {
    local service_key=$1
    local url=${SERVICE_URLS[$service_key]}

    print_info "Checking OSRM service: $service_key at $url"

    if curl -s --max-time $TIMEOUT "$url/" > /dev/null 2>&1; then
        print_success "OSRM service $service_key is running"
        return 0
    else
        print_error "OSRM service $service_key is not accessible at $url"
        return 1
    fi
}

# Function to check all required services
check_all_services() {
    local failed_services=()

    print_header "Checking OSRM Services"

    for service_key in "${!SERVICE_URLS[@]}"; do
        if ! check_osrm_service "$service_key"; then
            failed_services+=("$service_key")
        fi
    done

    if [ ${#failed_services[@]} -gt 0 ]; then
        print_error "Failed services: ${failed_services[*]}"
        print_info "Please ensure services are running with: docker-compose -f docker-compose-multi-profile.yml up -d"
        exit 1
    fi

    print_success "All OSRM services are running!"
    echo ""
}

# Function to format duration from seconds to human readable
format_duration() {
    local seconds=$1
    # Convert to integer to handle floating point values
    local int_seconds=$(echo "$seconds" | cut -d'.' -f1)
    local hours=$((int_seconds / 3600))
    local minutes=$(((int_seconds % 3600) / 60))
    local remaining_seconds=$((int_seconds % 60))

    if [ $hours -gt 0 ]; then
        printf "%dh %dm %ds" $hours $minutes $remaining_seconds
    elif [ $minutes -gt 0 ]; then
        printf "%dm %ds" $minutes $remaining_seconds
    else
        printf "%ds" $remaining_seconds
    fi
}

# Function to format distance from meters to km
format_distance() {
    local meters=$1
    local km=$(echo "scale=2; $meters / 1000" | bc -l)
    printf "%.2f km" $km
}

# Function to get route information for a specific mode and algorithm
get_route_info() {
    local from_coords=$1
    local to_coords=$2
    local from_name=$3
    local to_name=$4
    local mode=$5
    local algorithm=$6

    local service_key="${mode}-${algorithm}"
    local base_url=${SERVICE_URLS[$service_key]}

    if [ -z "$base_url" ]; then
        print_error "Unknown service: $service_key"
        return 1
    fi

    # Map mode to OSRM profile name
    local profile_name
    case $mode in
        car) profile_name="driving" ;;
        bicycle) profile_name="cycling" ;;
        foot) profile_name="walking" ;;
        *) profile_name="driving" ;;
    esac

    local url="$base_url/route/v1/$profile_name/$from_coords;$to_coords?overview=false&alternatives=false&steps=false"

    # Make API call with timeout
    local response=$(curl -s --max-time $TIMEOUT "$url" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$response" ]; then
        print_error "Failed to get route from $from_name to $to_name ($service_key - Network error)"
        return 1
    fi

    # Parse JSON response
    local code=$(echo "$response" | jq -r '.code // "Error"')

    if [ "$code" != "Ok" ]; then
        print_error "Failed to get route from $from_name to $to_name ($service_key - Code: $code)"
        return 1
    fi

    # Extract route information
    local distance=$(echo "$response" | jq -r '.routes[0].distance // 0')
    local duration=$(echo "$response" | jq -r '.routes[0].duration // 0')

    if [ "$distance" = "0" ] || [ "$duration" = "0" ]; then
        print_error "Invalid route data from $from_name to $to_name ($service_key)"
        return 1
    fi

    # Format and display results
    local formatted_distance=$(format_distance $distance)
    local formatted_duration=$(format_duration $duration)
    local avg_speed=$(echo "scale=1; $distance * 3.6 / $duration" | bc -l)

    print_mode "$mode" "$from_name → $to_name"
    print_algorithm "$algorithm" "Distance: $formatted_distance | Duration: $formatted_duration | Speed: ${avg_speed} km/h"
    echo ""

    return 0
}

# Function to test route with multiple modes and algorithms
test_route_multi_modal() {
    local from_coords=$1
    local to_coords=$2
    local from_name=$3
    local to_name=$4

    print_route "🌟 Multi-Modal Route: $from_name → $to_name"
    echo ""

    # Test based on selected mode and algorithm
    if [ "$MODE" = "all" ]; then
        local modes=("car" "bicycle" "foot")
    else
        local modes=("$MODE")
    fi

    if [ "$ALGORITHM" = "all" ]; then
        local algorithms=("mld" "ch")
    else
        local algorithms=("$ALGORITHM")
    fi

    for mode in "${modes[@]}"; do
        for algorithm in "${algorithms[@]}"; do
            get_route_info "$from_coords" "$to_coords" "$from_name" "$to_name" "$mode" "$algorithm"
        done
    done
}

# Function to test multi-waypoint route with trip optimization (uses CH algorithm)
test_multi_waypoint_route() {
    local waypoints=$1
    local route_name=$2
    local mode=${3:-car}

    print_route "🎯 Multi-waypoint Trip Optimization: $route_name"

    local service_key="${mode}-ch"  # Always use CH for trip optimization
    local base_url=${SERVICE_URLS[$service_key]}

    if [ -z "$base_url" ]; then
        print_error "CH service not available for $mode mode"
        return 1
    fi

    # Map mode to OSRM profile name
    local profile_name
    case $mode in
        car) profile_name="driving" ;;
        bicycle) profile_name="cycling" ;;
        foot) profile_name="walking" ;;
        *) profile_name="driving" ;;
    esac

    # Use trip service for multi-waypoint optimization
    local url="$base_url/trip/v1/$profile_name/$waypoints?overview=false&alternatives=false&steps=false"
    local response=$(curl -s --max-time $TIMEOUT "$url" 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$response" ]; then
        print_error "Failed to get optimized trip ($mode-CH - Network error)"
        return 1
    fi

    local code=$(echo "$response" | jq -r '.code // "Error"')

    if [ "$code" != "Ok" ]; then
        print_error "Failed to get optimized trip ($mode-CH - Code: $code)"
        return 1
    fi

    local distance=$(echo "$response" | jq -r '.trips[0].distance // 0')
    local duration=$(echo "$response" | jq -r '.trips[0].duration // 0')

    if [ "$distance" != "0" ] && [ "$duration" != "0" ]; then
        local formatted_distance=$(format_distance $distance)
        local formatted_duration=$(format_duration $duration)
        local avg_speed=$(echo "scale=1; $distance * 3.6 / $duration" | bc -l)

        print_mode "$mode" "Optimized Trip"
        print_algorithm "ch" "Distance: $formatted_distance | Duration: $formatted_duration | Speed: ${avg_speed} km/h"
        print_success "Multi-waypoint trip optimized successfully with CH algorithm"
    else
        print_error "Invalid optimized trip data"
        return 1
    fi
    echo ""
}

# Function to test multi-waypoint route with multiple modes
test_multi_waypoint_multi_modal() {
    local waypoints=$1
    local route_name=$2

    print_route "🌟 Multi-Modal Trip Optimization: $route_name"
    echo ""

    # Test based on selected mode
    if [ "$MODE" = "all" ]; then
        local modes=("car" "bicycle" "foot")
    else
        local modes=("$MODE")
    fi

    for mode in "${modes[@]}"; do
        test_multi_waypoint_route "$waypoints" "$route_name" "$mode"
    done
}

# Main execution
main() {
    print_header "OSRM Northern India Multi-Profile Route Demonstration"

    echo "🔧 Configuration:"
    echo "   Mode: $MODE"
    echo "   Algorithm: $ALGORITHM"
    echo ""

    # Check dependencies
    if ! command -v jq &> /dev/null; then
        print_error "jq is required but not installed. Please install jq first."
        exit 1
    fi

    if ! command -v bc &> /dev/null; then
        print_error "bc is required but not installed. Please install bc first."
        exit 1
    fi

    # Check OSRM services
    check_all_services

    print_header "Testing Individual Routes with Multiple Modes"

    # Test 1: Delhi NCR and nearby routes (short distances)
    print_info "1. Delhi NCR and Nearby Regional Routes"
    test_route_multi_modal "$DELHI" "$GURGAON" "Delhi" "Gurgaon"
    test_route_multi_modal "$DELHI" "$SONIPAT" "Delhi" "Sonipat"
    test_route_multi_modal "$DELHI" "$ROHTAK" "Delhi" "Rohtak"
    test_route_multi_modal "$GURGAON" "$PANIPAT" "Gurgaon" "Panipat"

    # Test 2: Medium distance routes within Northern India
    print_info "2. Medium Distance Routes"
    test_route_multi_modal "$DELHI" "$JAIPUR" "Delhi" "Jaipur"
    test_route_multi_modal "$DELHI" "$CHANDIGARH" "Delhi" "Chandigarh"
    test_route_multi_modal "$DELHI" "$AGRA" "Delhi" "Agra"
    test_route_multi_modal "$CHANDIGARH" "$SHIMLA" "Chandigarh" "Shimla"

    # Test 3: Long distance routes
    print_info "3. Long Distance Routes"
    test_route_multi_modal "$DELHI" "$AMRITSAR" "Delhi" "Amritsar"
    test_route_multi_modal "$CHANDIGARH" "$AMRITSAR" "Chandigarh" "Amritsar"
    test_route_multi_modal "$LUDHIANA" "$BATHINDA" "Ludhiana" "Bathinda"
    test_route_multi_modal "$JAIPUR" "$AGRA" "Jaipur" "Agra"

    # Test 4: Hill station routes
    print_info "4. Hill Station Routes"
    test_route_multi_modal "$DELHI" "$DEHRADUN" "Delhi" "Dehradun"
    test_route_multi_modal "$DEHRADUN" "$HARIDWAR" "Dehradun" "Haridwar"
    test_route_multi_modal "$SHIMLA" "$MANALI" "Shimla" "Manali"

    # Test 5: Punjab and Haryana routes
    print_info "5. Punjab and Haryana Routes"
    test_route_multi_modal "$AMBALA" "$LUDHIANA" "Ambala" "Ludhiana"
    test_route_multi_modal "$KARNAL" "$HISAR" "Karnal" "Hisar"

    print_header "Testing Multi-Waypoint Trip Optimization (CH Algorithm)"

    # Multi-waypoint route 1: Delhi and surrounding areas
    test_multi_waypoint_multi_modal "$DELHI;$SONIPAT;$PANIPAT;$KARNAL;$AMBALA;$CHANDIGARH" "Delhi to Chandigarh via Sonipat-Panipat-Karnal-Ambala"

    # Multi-waypoint route 2: Golden Triangle
    test_multi_waypoint_multi_modal "$DELHI;$AGRA;$JAIPUR;$DELHI" "Golden Triangle (Delhi-Agra-Jaipur)"

    # Multi-waypoint route 3: Punjab circuit
    test_multi_waypoint_multi_modal "$DELHI;$CHANDIGARH;$LUDHIANA;$AMRITSAR;$DELHI" "Punjab Circuit"

    # Multi-waypoint route 4: Haryana circuit
    test_multi_waypoint_multi_modal "$DELHI;$SONIPAT;$KARNAL;$AMBALA;$CHANDIGARH;$SHIMLA" "Delhi to Shimla via Haryana"

    # Multi-waypoint route 5: Hill stations tour
    test_multi_waypoint_multi_modal "$DELHI;$CHANDIGARH;$SHIMLA;$MANALI" "Northern Hill Stations Tour"

    print_header "Multi-Profile Route Testing Summary"
    print_success "All multi-profile route demonstrations completed successfully!"
    echo ""
    print_info "🚀 OSRM Multi-Profile Services Status:"
    echo ""

    # Display service URLs
    for service_key in "${!SERVICE_URLS[@]}"; do
        local url=${SERVICE_URLS[$service_key]}
        print_info "   $service_key: $url"
    done

    echo ""
    print_info "🌐 Additional Services:"
    print_info "   Frontend URL: http://localhost:9966"
    print_info "   Gateway URL: $GATEWAY_URL (if nginx gateway is running)"
    echo ""

    print_info "🔧 Usage Examples:"
    echo ""
    print_info "   Single mode testing:"
    print_info "   ./osrm-demo-routes.sh car mld"
    print_info "   ./osrm-demo-routes.sh bicycle ch"
    print_info "   ./osrm-demo-routes.sh foot all"
    echo ""
    print_info "   Multi-modal testing:"
    print_info "   ./osrm-demo-routes.sh all mld"
    print_info "   ./osrm-demo-routes.sh all ch"
    print_info "   ./osrm-demo-routes.sh all all"
    echo ""

    print_info "🎯 Key Features Demonstrated:"
    print_info "   ✅ Multiple transportation modes (car, bicycle, foot)"
    print_info "   ✅ Multiple algorithms (MLD for speed, CH for multi-waypoint optimization)"
    print_info "   ✅ Multi-waypoint trip optimization using CH algorithm"
    print_info "   ✅ Performance comparison between modes and algorithms"
    echo ""

    print_info "📊 For comparison with public OSRM API:"
    print_info "   Public API: https://router.project-osrm.org/route/v1/driving/{coordinates}"
    echo ""
}

# Run main function
main "$@"
