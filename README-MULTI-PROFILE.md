# OSRM Multi-Profile Setup for Northern India

This setup provides a comprehensive OSRM (Open Source Routing Machine) implementation for Northern India with support for multiple transportation modes and routing algorithms.

## 🚀 Features

### Transportation Modes
- **🚗 Car/Driving**: Optimized for motor vehicles with traffic rules and road restrictions
- **🚴 Bicycle/Cycling**: Bicycle-friendly routing with appropriate speed limits and path preferences
- **🚶 Walking/Foot**: Pedestrian routing with walking paths and appropriate speeds

### Routing Algorithms
- **⚡ MLD (Multi-Level Dijkstra)**: Fast for single-point to single-point routing
- **🎯 CH (Contraction Hierarchies)**: Optimized for multi-waypoint trip optimization

### Key Capabilities
- ✅ Multi-modal route comparison
- ✅ Multi-waypoint trip optimization using CH algorithm
- ✅ Performance comparison between algorithms
- ✅ Comprehensive API coverage (route, trip, table, match, nearest)
- ✅ Load balancing and API gateway support

## 📁 Project Structure

```
├── data/                                    # Processed OSRM data files
│   ├── northern-zone-latest-car-mld.osrm*     # Car profile with MLD algorithm
│   ├── northern-zone-latest-car-ch.osrm*      # Car profile with CH algorithm
│   ├── northern-zone-latest-bicycle-mld.osrm* # Bicycle profile with MLD algorithm
│   ├── northern-zone-latest-bicycle-ch.osrm*  # Bicycle profile with CH algorithm
│   ├── northern-zone-latest-foot-mld.osrm*    # Foot profile with MLD algorithm
│   └── northern-zone-latest-foot-ch.osrm*     # Foot profile with CH algorithm
├── profiles/                                # OSRM routing profiles
│   ├── car.lua                             # Car routing profile
│   ├── bicycle.lua                         # Bicycle routing profile
│   └── foot.lua                            # Walking routing profile
├── docker-compose-multi-profile.yml        # Multi-profile Docker services
├── nginx-multi-profile.conf               # API gateway configuration
├── process-northern-zone-multi.sh         # Data processing script
└── osrm-demo-routes.sh                    # Enhanced demo script
```

## 🛠️ Setup Instructions

### 1. Data Processing

Process the Northern India OSM data for all profiles and algorithms:

```bash
# Process all combinations (car, bicycle, foot) × (mld, ch)
./process-northern-zone-multi.sh

# Or process specific combinations
./process-northern-zone-multi.sh "car,bicycle" "mld,ch"
./process-northern-zone-multi.sh "car" "ch"
```

### 2. Start Services

Launch all OSRM services with different profiles and algorithms:

```bash
# Start all multi-profile services
docker-compose -f docker-compose-multi-profile.yml up -d

# Check service status
docker-compose -f docker-compose-multi-profile.yml ps
```

### 3. Service Endpoints

| Service | Profile | Algorithm | Port | URL |
|---------|---------|-----------|------|-----|
| Car MLD | car | mld | 5000 | http://localhost:5000 |
| Car CH | car | ch | 5001 | http://localhost:5001 |
| Bicycle MLD | bicycle | mld | 5010 | http://localhost:5010 |
| Bicycle CH | bicycle | ch | 5011 | http://localhost:5011 |
| Foot MLD | foot | mld | 5020 | http://localhost:5020 |
| Foot CH | foot | ch | 5021 | http://localhost:5021 |
| Gateway | all | all | 8080 | http://localhost:8080 |
| Frontend | - | - | 9966 | http://localhost:9966 |

## 🧪 Testing and Demo

### Run Demo Script

The enhanced demo script showcases all transportation modes and algorithms:

```bash
# Test all modes and algorithms
./osrm-demo-routes.sh

# Test specific mode and algorithm
./osrm-demo-routes.sh car mld
./osrm-demo-routes.sh bicycle ch
./osrm-demo-routes.sh foot all

# Test all modes with specific algorithm
./osrm-demo-routes.sh all ch
```

### API Examples

#### Single Route (MLD Algorithm)
```bash
# Car route
curl "http://localhost:5000/route/v1/driving/77.209,28.6139;77.0266,28.4595"

# Bicycle route
curl "http://localhost:5010/route/v1/cycling/77.209,28.6139;77.0266,28.4595"

# Walking route
curl "http://localhost:5020/route/v1/walking/77.209,28.6139;77.0266,28.4595"
```

#### Multi-waypoint Trip Optimization (CH Algorithm)
```bash
# Optimized car trip
curl "http://localhost:5001/trip/v1/driving/77.209,28.6139;77.0266,28.4595;76.7794,30.7333"

# Optimized bicycle trip
curl "http://localhost:5011/trip/v1/cycling/77.209,28.6139;77.0266,28.4595;76.7794,30.7333"
```

#### Using API Gateway
```bash
# Car routes (MLD by default)
curl "http://localhost:8080/route/v1/driving/77.209,28.6139;77.0266,28.4595"

# Car routes with CH algorithm
curl "http://localhost:8080/route/v1/driving-ch/77.209,28.6139;77.0266,28.4595"

# Bicycle routes
curl "http://localhost:8080/route/v1/cycling/77.209,28.6139;77.0266,28.4595"

# Trip optimization (uses CH automatically)
curl "http://localhost:8080/trip/v1/driving/77.209,28.6139;77.0266,28.4595;76.7794,30.7333"
```

## 🔧 Configuration

### Docker Compose Services

Each service is configured with:
- **Memory limit**: 4GB per service
- **CPU threads**: 2 threads per service
- **Health checks**: Automatic service monitoring
- **Restart policy**: Automatic restart on failure

### Algorithm Selection Guidelines

- **Use MLD** for:
  - Single point-to-point routing
  - Real-time route calculations
  - When speed is more important than optimization

- **Use CH** for:
  - Multi-waypoint trip optimization
  - Traveling salesman problem (TSP) scenarios
  - When route optimization is more important than speed

## 📊 Performance Comparison

The demo script provides performance comparisons showing:
- **Distance differences** between transportation modes
- **Duration variations** based on mode-specific speeds
- **Algorithm performance** for different use cases

Example output:
```
🚗 Delhi → Gurgaon
⚡ MLD: Distance: 28.45 km | Duration: 45m 30s | Speed: 37.5 km/h
🎯 CH: Distance: 28.45 km | Duration: 45m 30s | Speed: 37.5 km/h

🚴 Delhi → Gurgaon  
⚡ MLD: Distance: 29.12 km | Duration: 1h 52m 15s | Speed: 15.6 km/h
🎯 CH: Distance: 29.12 km | Duration: 1h 52m 15s | Speed: 15.6 km/h

🚶 Delhi → Gurgaon
⚡ MLD: Distance: 30.05 km | Duration: 6h 1m 0s | Speed: 5.0 km/h
🎯 CH: Distance: 30.05 km | Duration: 6h 1m 0s | Speed: 5.0 km/h
```

## 🚨 Troubleshooting

### Common Issues

1. **Service not starting**: Check if data files exist for the specific profile-algorithm combination
2. **Memory issues**: Increase Docker memory limits in docker-compose file
3. **Port conflicts**: Ensure ports 5000-5021, 8080, 9966 are available

### Logs and Monitoring

```bash
# Check service logs
docker-compose -f docker-compose-multi-profile.yml logs osrm-car-mld
docker-compose -f docker-compose-multi-profile.yml logs osrm-bicycle-ch

# Monitor resource usage
docker stats
```

## 🔄 Data Updates

To update the routing data:

1. Download new OSM data for Northern India
2. Run the processing script: `./process-northern-zone-multi.sh`
3. Restart services: `docker-compose -f docker-compose-multi-profile.yml restart`

## 📈 Scaling

For production deployment:
- Use separate servers for different profiles
- Implement proper load balancing
- Add monitoring and alerting
- Consider using Kubernetes for orchestration
