events {
    worker_connections 1024;
}

http {
    upstream osrm-car-mld {
        server osrm-car-mld:5000;
    }
    
    upstream osrm-car-ch {
        server osrm-car-ch:5000;
    }
    
    upstream osrm-bicycle-mld {
        server osrm-bicycle-mld:5000;
    }
    
    upstream osrm-bicycle-ch {
        server osrm-bicycle-ch:5000;
    }
    
    upstream osrm-foot-mld {
        server osrm-foot-mld:5000;
    }
    
    upstream osrm-foot-ch {
        server osrm-foot-ch:5000;
    }

    server {
        listen 80;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Car profile with MLD algorithm (default)
        location /route/v1/driving/ {
            proxy_pass http://osrm-car-mld/route/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Car profile with CH algorithm (for multi-waypoint optimization)
        location /route/v1/driving-ch/ {
            proxy_pass http://osrm-car-ch/route/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Bicycle profile with MLD algorithm
        location /route/v1/cycling/ {
            proxy_pass http://osrm-bicycle-mld/route/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Bicycle profile with CH algorithm (for multi-waypoint optimization)
        location /route/v1/cycling-ch/ {
            proxy_pass http://osrm-bicycle-ch/route/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Foot profile with MLD algorithm
        location /route/v1/walking/ {
            proxy_pass http://osrm-foot-mld/route/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Foot profile with CH algorithm (for multi-waypoint optimization)
        location /route/v1/walking-ch/ {
            proxy_pass http://osrm-foot-ch/route/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Trip service (multi-waypoint optimization) - defaults to CH algorithm
        location /trip/v1/driving/ {
            proxy_pass http://osrm-car-ch/trip/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /trip/v1/cycling/ {
            proxy_pass http://osrm-bicycle-ch/trip/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /trip/v1/walking/ {
            proxy_pass http://osrm-foot-ch/trip/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Table service (distance matrix)
        location /table/v1/driving/ {
            proxy_pass http://osrm-car-mld/table/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /table/v1/cycling/ {
            proxy_pass http://osrm-bicycle-mld/table/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /table/v1/walking/ {
            proxy_pass http://osrm-foot-mld/table/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Match service (map matching)
        location /match/v1/driving/ {
            proxy_pass http://osrm-car-mld/match/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /match/v1/cycling/ {
            proxy_pass http://osrm-bicycle-mld/match/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /match/v1/walking/ {
            proxy_pass http://osrm-foot-mld/match/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Nearest service
        location /nearest/v1/driving/ {
            proxy_pass http://osrm-car-mld/nearest/v1/driving/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /nearest/v1/cycling/ {
            proxy_pass http://osrm-bicycle-mld/nearest/v1/cycling/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        location /nearest/v1/walking/ {
            proxy_pass http://osrm-foot-mld/nearest/v1/walking/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # Default fallback to car-mld
        location / {
            proxy_pass http://osrm-car-mld/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
}
